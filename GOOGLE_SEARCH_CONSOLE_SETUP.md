# Настро<PERSON><PERSON><PERSON> Google Search Console для shop.sadi.kz

## 🎯 Главное правило

**В Google Search Console указывать ТОЛЬКО один sitemap:**

```
https://shop.sadi.kz/sitemap-index.xml
```

## ❌ НЕ добавлять отдельно:

- ~~https://shop.sadi.kz/sitemap.xml~~
- ~~https://shop.sadi.kz/sitemap-pages.xml~~
- ~~https://shop.sadi.kz/sitemap-categories.xml~~
- ~~https://shop.sadi.kz/sitemap-products.xml~~

## ✅ Почему только sitemap-index.xml:

1. **Автоматическое обнаружение**: Google автоматически найдет все дочерние sitemap
2. **Избежание дублирования**: Предотвращает конфликты между sitemap файлами
3. **Стандартный подход**: Соответствует рекомендациям Google для больших сайтов
4. **Легкое управление**: Один URL вместо управления 4-мя отдельными

## 📊 Что будет проиндексировано:

После добавления sitemap-index.xml, Google найдет и проиндексирует:

- **sitemap.xml**: ~5 статических страниц
- **sitemap-pages.xml**: 500 страниц каталога
- **sitemap-categories.xml**: страницы категорий (количество зависит от API)
- **sitemap-products.xml**: до 1000 товаров

## 🔍 Мониторинг в GSC:

1. Перейти в **Индексирование → Файлы Sitemap**
2. Добавить: `https://shop.sadi.kz/sitemap-index.xml`
3. Отслеживать:
   - Количество отправленных URL
   - Количество проиндексированных URL
   - Ошибки индексации
   - Статус каждого дочернего sitemap

## 🚀 Дополнительные рекомендации:

1. **Проверка robots.txt**: Убедиться что robots.txt правильно ссылается на sitemap-index.xml
2. **Регулярный мониторинг**: Проверять статус индексации раз в неделю
3. **Анализ покрытия**: Отслеживать какие страницы не индексируются
4. **Производительность**: Мониторить Core Web Vitals для проиндексированных страниц

## 🔧 Troubleshooting:

Если возникают проблемы:

1. Проверить доступность sitemap-index.xml в браузере
2. Убедиться что все дочерние sitemap доступны
3. Проверить отсутствие ошибок в XML разметке
4. Убедиться что robots.txt не блокирует sitemap файлы

## 📈 Ожидаемые результаты:

- Индексация основных страниц: 1-3 дня
- Полная индексация каталога: 1-2 недели
- Индексация товаров: 2-4 недели

Общее количество проиндексированных страниц должно составить ~1500+ URL.
