// Умная система генерации ключевых слов для 100,000+ товаров

// Расширенный словарь материалов и их ключевых слов
export const MATERIAL_KEYWORDS = {
  // Бетон и растворы
  бетон: [
    "бетон марки",
    "бетонная смесь",
    "товарный бетон",
    "бетон класса",
    "тяжелый бетон",
    "легкий бетон",
    "монолитный бетон",
    "сборный бетон",
    "бетон с доставкой",
    "бетон миксер",
    "бетон завод",
    "бетон цена за куб",
    "бетон м200",
    "бетон м300",
    "бетон м350",
    "бетон м400",
    "бетон м500",
    "бетон в15",
    "бетон в20",
    "бетон в25",
    "бетон в30",
    "бетон в35",
    "бетон для фундамента",
    "бетон для стяжки",
    "бетон для дорог",
    "высокопрочный бетон",
    "морозостойкий бетон",
    "водонепроницаемый бетон",
  ],
  раствор: [
    "строительный раствор",
    "цементный раствор",
    "кладочный раствор",
    "штукатурный раствор",
    "монтажный раствор",
    "цементно-песчаный раствор",
    "известковый раствор",
    "гипсовый раствор",
    "полимерный раствор",
    "раствор для кладки",
    "раствор для штукатурки",
    "раствор для стяжки",
    "готовый раствор",
    "сухой раствор",
    "раствор марки",
    "раствор м100",
    "раствор м150",
    "раствор м200",
    "раствор м300",
  ],
  смесь: [
    "сухая смесь",
    "строительная смесь",
    "готовая смесь",
    "клеевая смесь",
    "выравнивающая смесь",
    "ремонтная смесь",
    "гидроизоляционная смесь",
    "теплоизоляционная смесь",
    "декоративная смесь",
    "смесь для пола",
    "смесь для стен",
    "смесь для потолка",
    "быстротвердеющая смесь",
    "самовыравнивающаяся смесь",
    "антисептическая смесь",
  ],

  // Сыпучие материалы
  щебень: [
    "щебень фракции",
    "гранитный щебень",
    "известняковый щебень",
    "щебень для дорог",
    "щебень для бетона",
    "декоративный щебень",
    "щебень 5-20",
    "щебень 20-40",
    "щебень 40-70",
    "щебень гост",
    "щебень с доставкой",
    "щебень цена за тонну",
    "щебень цена за куб",
    "дорожный щебень",
    "строительный щебень",
    "щебень для фундамента",
    "щебень для дренажа",
    "мраморный щебень",
    "базальтовый щебень",
  ],
  песок: [
    "строительный песок",
    "речной песок",
    "карьерный песок",
    "песок для бетона",
    "кварцевый песок",
    "мытый песок",
    "песок с доставкой",
    "песок цена за тонну",
    "песок цена за куб",
    "песок для стяжки",
    "песок для кладки",
    "песок для штукатурки",
    "песок мелкий",
    "песок средний",
    "песок крупный",
    "песок модуль крупности",
    "песок гост",
    "морской песок",
    "горный песок",
  ],
  гравий: [
    "строительный гравий",
    "гравий фракции",
    "речной гравий",
    "морской гравий",
    "горный гравий",
    "гравий 5-20",
    "гравий 20-40",
    "гравий для бетона",
    "гравий для дренажа",
    "гравий с доставкой",
    "гравий цена за тонну",
    "декоративный гравий",
    "цветной гравий",
  ],
  керамзит: [
    "керамзит фракции",
    "керамзитовый гравий",
    "утеплитель керамзит",
    "керамзитобетон",
    "легкий заполнитель",
    "керамзит 10-20",
    "керамзит 5-10",
    "керамзит 20-40",
    "керамзит насыпная плотность",
    "керамзит теплопроводность",
    "керамзит для стяжки",
    "керамзит для утепления",
    "керамзит цена за куб",
  ],

  // Кирпич и блоки
  кирпич: [
    "строительный кирпич",
    "облицовочный кирпич",
    "керамический кирпич",
    "силикатный кирпич",
    "огнеупорный кирпич",
    "клинкерный кирпич",
  ],
  блок: [
    "строительный блок",
    "газобетонный блок",
    "пеноблок",
    "керамзитобетонный блок",
    "шлакоблок",
    "арболитовый блок",
  ],
  газобетон: [
    "газобетонные блоки",
    "автоклавный газобетон",
    "ячеистый бетон",
    "газосиликат",
    "пористый бетон",
  ],

  // Вяжущие материалы
  цемент: [
    "портландцемент",
    "цемент марки",
    "цемент м400",
    "цемент м500",
    "быстротвердеющий цемент",
    "белый цемент",
    "сульфатостойкий цемент",
    "цемент пц400",
    "цемент пц500",
    "цемент д0",
    "цемент д20",
    "цемент с доставкой",
    "цемент цена за тонну",
    "цемент цена за мешок",
    "цемент 50 кг",
    "цемент навалом",
    "цемент в мешках",
    "цемент гост",
    "цемент для фундамента",
    "цемент для стяжки",
    "шлакопортландцемент",
    "пуццолановый цемент",
    "глиноземистый цемент",
  ],
  известь: [
    "строительная известь",
    "гашеная известь",
    "негашеная известь",
    "известковое тесто",
    "известковое молоко",
    "известь пушонка",
    "известь комовая",
    "известь молотая",
    "известь гидратная",
    "известь для штукатурки",
    "известь для побелки",
    "известь цена за тонну",
    "известь с доставкой",
  ],
  гипс: [
    "строительный гипс",
    "гипсовая смесь",
    "алебастр",
    "формовочный гипс",
    "медицинский гипс",
    "гипс г4",
    "гипс г5",
    "гипс г6",
    "гипс г7",
    "гипс для штукатурки",
    "гипс для лепнины",
    "гипс быстротвердеющий",
    "гипс цена за мешок",
  ],

  // Металлопрокат
  арматура: [
    "арматурная сталь",
    "рифленая арматура",
    "гладкая арматура",
    "композитная арматура",
    "стеклопластиковая арматура",
  ],
  профнастил: [
    "профилированный лист",
    "кровельный профнастил",
    "стеновой профнастил",
    "оцинкованный профнастил",
    "окрашенный профнастил",
  ],
  металлочерепица: [
    "кровельная металлочерепица",
    "профилированная металлочерепица",
    "композитная металлочерепица",
    "стальная металлочерепица",
  ],
  труба: [
    "металлическая труба",
    "стальная труба",
    "профильная труба",
    "водопроводная труба",
    "канализационная труба",
  ],

  // Кровельные материалы
  черепица: [
    "кровельная черепица",
    "керамическая черепица",
    "цементно-песчаная черепица",
    "битумная черепица",
    "композитная черепица",
  ],
  шифер: [
    "асбестоцементный шифер",
    "волновой шифер",
    "плоский шифер",
    "еврошифер",
    "ондулин",
  ],
  рубероид: [
    "кровельный рубероид",
    "подкладочный рубероид",
    "наплавляемый рубероид",
    "самоклеящийся рубероид",
    "армированный рубероид",
  ],

  // Изоляционные материалы
  утеплитель: [
    "теплоизоляция",
    "минеральная вата",
    "пенополистирол",
    "экструдированный пенополистирол",
    "пенополиуретан",
  ],
  пенопласт: [
    "пенополистирол",
    "теплоизоляция пенопласт",
    "утеплитель пенопласт",
    "фасадный пенопласт",
    "упаковочный пенопласт",
  ],
  минвата: [
    "минеральная вата",
    "базальтовая вата",
    "каменная вата",
    "стекловата",
    "шлаковата",
  ],

  // Отделочные материалы
  плитка: [
    "керамическая плитка",
    "напольная плитка",
    "настенная плитка",
    "керамогранит",
    "мозаика",
    "клинкерная плитка",
  ],
  ламинат: [
    "напольный ламинат",
    "влагостойкий ламинат",
    "ламинированное покрытие",
    "паркетная доска",
    "инженерная доска",
  ],
  линолеум: [
    "напольный линолеум",
    "коммерческий линолеум",
    "бытовой линолеум",
    "полукоммерческий линолеум",
    "спортивный линолеум",
  ],

  // Лакокрасочные материалы
  краска: [
    "строительная краска",
    "фасадная краска",
    "интерьерная краска",
    "водоэмульсионная краска",
    "акриловая краска",
    "масляная краска",
  ],
  эмаль: [
    "алкидная эмаль",
    "акриловая эмаль",
    "защитная эмаль",
    "антикоррозийная эмаль",
    "термостойкая эмаль",
  ],
  грунтовка: [
    "строительная грунтовка",
    "антикоррозийная грунтовка",
    "адгезионная грунтовка",
    "глубокого проникновения",
    "универсальная грунтовка",
  ],

  // Инструменты и крепеж
  саморез: [
    "строительные саморезы",
    "кровельные саморезы",
    "саморезы по металлу",
    "саморезы по дереву",
    "саморезы с пресс-шайбой",
  ],
  гвоздь: [
    "строительные гвозди",
    "кровельные гвозди",
    "финишные гвозди",
    "толевые гвозди",
    "дюбель-гвозди",
  ],
  анкер: [
    "анкерные болты",
    "химические анкеры",
    "механические анкеры",
    "клиновые анкеры",
    "распорные анкеры",
  ],

  // Сантехника и водоснабжение
  фитинг: [
    "сантехнические фитинги",
    "трубные фитинги",
    "соединительные фитинги",
    "переходные фитинги",
    "угловые фитинги",
  ],
  вентиль: [
    "запорная арматура",
    "шаровые краны",
    "задвижки",
    "регулирующие вентили",
    "обратные клапаны",
  ],

  // Электротехнические материалы
  кабель: [
    "электрический кабель",
    "силовой кабель",
    "контрольный кабель",
    "телефонный кабель",
    "интернет кабель",
  ],
  провод: [
    "электрический провод",
    "монтажный провод",
    "установочный провод",
    "гибкий провод",
    "одножильный провод",
  ],

  // Дополнительные категории для лучшего SEO
  // Древесные материалы
  доска: [
    "строительная доска",
    "обрезная доска",
    "необрезная доска",
    "половая доска",
    "террасная доска",
    "доска сухая",
    "доска естественной влажности",
    "доска хвойных пород",
    "доска лиственных пород",
    "доска цена за куб",
  ],
  брус: [
    "строительный брус",
    "клееный брус",
    "профилированный брус",
    "брус естественной влажности",
    "брус камерной сушки",
    "брус хвойный",
    "брус цена за куб",
  ],
  фанера: [
    "строительная фанера",
    "влагостойкая фанера",
    "ламинированная фанера",
    "березовая фанера",
    "хвойная фанера",
    "фанера фк",
    "фанера фсф",
  ],

  // Гидроизоляционные материалы
  гидроизоляция: [
    "гидроизоляционные материалы",
    "битумная гидроизоляция",
    "полимерная гидроизоляция",
    "проникающая гидроизоляция",
    "обмазочная гидроизоляция",
    "рулонная гидроизоляция",
    "гидроизоляция фундамента",
    "гидроизоляция кровли",
  ],
  мембрана: [
    "гидроизоляционная мембрана",
    "пароизоляционная мембрана",
    "диффузионная мембрана",
    "супердиффузионная мембрана",
    "кровельная мембрана",
  ],

  // Системы водоотведения
  желоб: [
    "водосточный желоб",
    "металлический желоб",
    "пластиковый желоб",
    "система водостока",
    "водосточная система",
  ],
  водосток: [
    "водосточная система",
    "водосточные трубы",
    "водосточные желоба",
    "элементы водостока",
  ],
};

// Функция для автоматической генерации ключевых слов
export function generateSmartKeywords(materialName) {
  const keywords = [];
  const name = materialName.toLowerCase();

  // Ищем совпадения в словаре и добавляем ключевые слова
  Object.entries(MATERIAL_KEYWORDS).forEach(([material, keywordList]) => {
    if (name.includes(material)) {
      keywords.push(...keywordList);
    }
  });

  // Добавляем технические характеристики из названия
  const technicalKeywords = extractTechnicalKeywords(name);
  keywords.push(...technicalKeywords);

  // Добавляем коммерческие ключевые слова
  const commercialKeywords = generateCommercialKeywords(materialName);
  keywords.push(...commercialKeywords);

  // Добавляем синонимы и вариации
  const synonymKeywords = generateSynonymKeywords(materialName);
  keywords.push(...synonymKeywords);

  // Убираем дубликаты
  return [...new Set(keywords)];
}

// Генерация коммерческих ключевых слов
function generateCommercialKeywords(materialName) {
  const name = materialName.toLowerCase();
  const commercialTerms = [
    "купить",
    "заказать",
    "цена",
    "стоимость",
    "доставка",
    "оптом",
    "в розницу",
    "со склада",
    "недорого",
    "дешево",
    "выгодно",
    "качественный",
    "сертифицированный",
    "гарантия",
    "быстрая доставка",
    "самовывоз",
    "в наличии",
    "под заказ",
  ];

  return commercialTerms.map((term) => `${name} ${term}`);
}

// Генерация синонимов и вариаций
function generateSynonymKeywords(materialName) {
  const keywords = [];
  const name = materialName.toLowerCase();

  // Добавляем варианты с "строительный"
  keywords.push(`строительный ${name}`);
  keywords.push(`${name} строительный`);

  // Добавляем варианты с "материал"
  keywords.push(`${name} материал`);
  keywords.push(`материал ${name}`);

  // Добавляем варианты с "для строительства"
  keywords.push(`${name} для строительства`);
  keywords.push(`${name} для ремонта`);

  // Добавляем варианты с качественными характеристиками
  const qualityTerms = ["качественный", "прочный", "надежный", "долговечный"];
  qualityTerms.forEach((term) => {
    keywords.push(`${term} ${name}`);
  });

  return keywords;
}

// Извлечение технических характеристик из названия
function extractTechnicalKeywords(materialName) {
  const keywords = [];

  // ГОСТ и стандарты
  const gostMatch = materialName.match(/гост\s*(\d+[-\d]*)/gi);
  if (gostMatch) {
    gostMatch.forEach((gost) => {
      keywords.push(gost.toLowerCase());
      keywords.push(`${gost.toLowerCase()} стандарт`);
    });
  }

  // Марки и классы
  const markaMatch = materialName.match(/м\d+/gi);
  if (markaMatch) {
    markaMatch.forEach((marka) => {
      keywords.push(`${marka.toLowerCase()} марка`);
      keywords.push(`марка ${marka.toLowerCase()}`);
    });
  }

  // Классы бетона
  const classMatch = materialName.match(/в\d+[.,]?\d*/gi);
  if (classMatch) {
    classMatch.forEach((klass) => {
      keywords.push(`класс ${klass.toLowerCase()}`);
      keywords.push(`${klass.toLowerCase()} класс`);
    });
  }

  // Фракции
  const fractionMatch = materialName.match(/\d+[-х]\d+/gi);
  if (fractionMatch) {
    fractionMatch.forEach((fraction) => {
      keywords.push(`фракция ${fraction}`);
      keywords.push(`${fraction} фракция`);
    });
  }

  // Размеры
  const sizeMatch = materialName.match(/\d+х\d+х?\d*/gi);
  if (sizeMatch) {
    sizeMatch.forEach((size) => {
      keywords.push(`размер ${size}`);
      keywords.push(`${size} размер`);
    });
  }

  return keywords;
}

// Генерация локальных ключевых слов
export function generateLocalKeywords(
  baseKeywords,
  cities = [
    "Астана",
    "Алматы",
    "Шымкент",
    "Караганда",
    "Актобе",
    "Тараз",
    "Павлодар",
    "Усть-Каменогорск",
    "Семей",
    "Атырау",
    "Костанай",
    "Кызылорда",
    "Уральск",
    "Петропавловск",
    "Актау",
  ]
) {
  const localKeywords = [];
  const regions = ["Казахстан", "РК", "Республика Казахстан"];

  cities.forEach((city) => {
    localKeywords.push(
      `${baseKeywords} ${city}`,
      `купить ${baseKeywords} ${city}`,
      `доставка ${baseKeywords} ${city}`,
      `цена ${baseKeywords} ${city}`,
      `поставщики ${baseKeywords} ${city}`,
      `${baseKeywords} в ${city}`,
      `${baseKeywords} ${city} недорого`,
      `${baseKeywords} ${city} оптом`,
      `${baseKeywords} ${city} склад`,
      `заказать ${baseKeywords} ${city}`,
      `${baseKeywords} ${city} с доставкой`
    );
  });

  // Добавляем общие региональные ключевые слова
  regions.forEach((region) => {
    localKeywords.push(
      `${baseKeywords} ${region}`,
      `купить ${baseKeywords} ${region}`,
      `доставка ${baseKeywords} ${region}`,
      `${baseKeywords} по всему ${region}`,
      `${baseKeywords} в ${region}`
    );
  });

  return localKeywords;
}

export default {
  MATERIAL_KEYWORDS,
  generateSmartKeywords,
  generateLocalKeywords,
};
