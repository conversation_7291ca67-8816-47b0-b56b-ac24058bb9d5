"use client";

import React, { useEffect, useRef, useCallback } from 'react';

// Компонент-обертка для контейнера карты, предотвращающий повторный рендеринг
const MapWrapper = React.memo(
  ({ containerId }) => {
    return (
      <div 
        id={containerId} 
        style={{ 
          width: '100%', 
          height: '100%',
          borderRadius: '8px',
          overflow: 'hidden'
        }} 
      />
    );
  },
  () => true, // Всегда предотвращаем повторный рендеринг
);

const Map2GIS = ({ companyData, className, style }) => {
  const mapInstanceRef = useRef(null);
  const containerIdRef = useRef(`map-container-${Date.now()}-${Math.random()}`);

  // Функция для получения координат по RegionId
  const getCoordinatesByRegionId = useCallback((regionId) => {
    const regionCoords = {
      '01': [71.4704, 51.1801], // Нур-Султан
      '02': [76.9286, 43.2220], // Алматы
      '3 ': [69.5992, 42.3417], // Шымкент (обратите внимание на пробел)
      '04': [57.2076, 50.2839], // Актюбинская область (Актобе)
      '05': [76.9286, 43.2220], // Алматинская область (используем координаты Алматы)
      '06': [51.9244, 47.1164], // Атырауская область (Атырау)
      '07': [51.2070, 51.2357], // Западно-Казахстанская область (Уральск)
      '08': [71.3660, 42.9000], // Жамбылская область (Тараз)
      '09': [73.1094, 49.8047], // Карагандинская область (Караганда)
      '10': [63.5850, 53.2138], // Костанайская область (Костанай)
      '11': [65.5092, 44.8479], // Кызылординская область (Кызылорда)
      '12': [51.2081, 43.6566], // Мангистауская область (Актау)
      '13': [68.2665, 41.3084], // Туркестанская область (Туркестан)
      '14': [76.9574, 52.2873], // Павлодарская область (Павлодар)
      '15': [69.1450, 54.8750], // Северо-Казахстанская область (Петропавловск)
      '16': [82.6286, 49.9480], // Восточно-Казахстанская область (Усть-Каменогорск)
      '17': [71.4704, 51.1801], // Акмолинская область (используем координаты Нур-Султана)
    };

    return regionCoords[regionId] || [71.4704, 51.1801]; // По умолчанию Нур-Султан
  }, []);

  // Функция для получения названия региона по ID
  const getRegionNameById = useCallback((regionId) => {
    const regions = {
      '01': 'Нур-Султан',
      '02': 'Алматы',
      '3 ': 'Шымкент',
      '04': 'Актобе',
      '05': 'Алматинская область',
      '06': 'Атырау',
      '07': 'Уральск',
      '08': 'Тараз',
      '09': 'Караганда',
      '10': 'Костанай',
      '11': 'Кызылорда',
      '12': 'Актау',
      '13': 'Туркестан',
      '14': 'Павлодар',
      '15': 'Петропавловск',
      '16': 'Усть-Каменогорск',
      '17': 'Акмолинская область'
    };

    return regions[regionId] || 'Не указан';
  }, []);

  // Функция для геокодирования адреса через API 2ГИС
  const geocodeAddress = useCallback(async (address, regionId) => {
    if (!address) {
      return getCoordinatesByRegionId(regionId);
    }

    try {
      // Формируем полный адрес с учетом региона
      const regionName = getRegionNameById(regionId);
      let fullAddress = address;

      // Если в адресе нет названия города/региона, добавляем его
      const addressLower = address.toLowerCase();
      const regionLower = regionName.toLowerCase();

      if (!addressLower.includes(regionLower) &&
          !addressLower.includes('нур-султан') &&
          !addressLower.includes('астана') &&
          !addressLower.includes('алматы') &&
          !addressLower.includes('шымкент') &&
          !addressLower.includes('актобе') &&
          !addressLower.includes('караганда') &&
          !addressLower.includes('тараз') &&
          !addressLower.includes('павлодар') &&
          !addressLower.includes('атырау') &&
          !addressLower.includes('костанай') &&
          !addressLower.includes('кызылорда') &&
          !addressLower.includes('актау') &&
          !addressLower.includes('уральск') &&
          !addressLower.includes('петропавловск') &&
          !addressLower.includes('темиртау') &&
          !addressLower.includes('туркестан')) {
        fullAddress = `${regionName}, ${address}`;
      }

      // Запрос к API геокодирования 2ГИС
      const encodedAddress = encodeURIComponent(fullAddress);
      const geocodeUrl = `https://catalog.api.2gis.com/3.0/items/geocode?q=${encodedAddress}&fields=items.point&key=a1220568-7eca-457e-8dbf-be894c8fa7e6`;

      const response = await fetch(geocodeUrl);
      const data = await response.json();

      if (data.result && data.result.items && data.result.items.length > 0) {
        const item = data.result.items[0];
        if (item.point) {
          return [item.point.lon, item.point.lat]; // 2ГИС возвращает lat/lon, а нам нужно lon/lat
        }
      }

      // Если геокодирование не удалось, используем координаты региона
      console.warn('Геокодирование не удалось для адреса:', fullAddress);
      return getCoordinatesByRegionId(regionId);

    } catch (error) {
      console.error('Ошибка геокодирования:', error);
      // В случае ошибки используем координаты региона
      return getCoordinatesByRegionId(regionId);
    }
  }, [getCoordinatesByRegionId, getRegionNameById]);

  // Функция для получения координат по адресу с учетом региона (fallback)
  const getCoordinatesFromAddress = useCallback((address, regionId) => {
    // Сначала пытаемся определить координаты по RegionId
    const regionCoords = getCoordinatesByRegionId(regionId);

    if (!address) return regionCoords;

    // Дополнительно проверяем адрес на наличие конкретных городов
    const cityCoords = {
      'нур-султан': [71.4704, 51.1801],
      'астана': [71.4704, 51.1801],
      'алматы': [76.9286, 43.2220],
      'шымкент': [69.5992, 42.3417],
      'актобе': [57.2076, 50.2839],
      'караганда': [73.1094, 49.8047],
      'тараз': [71.3660, 42.9000],
      'павлодар': [76.9574, 52.2873],
      'усть-каменогорск': [82.6286, 49.9480],
      'семей': [80.2275, 50.4111],
      'атырау': [51.9244, 47.1164],
      'костанай': [63.5850, 53.2138],
      'кызылорда': [65.5092, 44.8479],
      'актау': [51.2081, 43.6566],
      'уральск': [51.2070, 51.2357],
      'петропавловск': [69.1450, 54.8750],
      'темиртау': [72.9633, 50.0547],
      'туркестан': [68.2665, 41.3084]
    };

    const addressLower = address.toLowerCase();

    // Проверяем, есть ли в адресе конкретный город
    for (const [city, coords] of Object.entries(cityCoords)) {
      if (addressLower.includes(city)) {
        return coords;
      }
    }

    // Если конкретный город не найден, используем координаты региона
    return regionCoords;
  }, [getCoordinatesByRegionId]);

  // Функция для получения адреса для отображения
  const getDisplayAddress = useCallback(() => {
    if (!companyData) return 'Адрес не указан';

    // Приоритет: ActualAddress -> LegalAddress -> RegionName
    const address = companyData.ActualAddress || companyData.LegalAddress;
    const regionName = getRegionNameById(companyData.RegionId);

    if (address && regionName) {
      // Если в адресе уже есть название региона/города, не дублируем
      const addressLower = address.toLowerCase();
      const regionLower = regionName.toLowerCase();

      if (addressLower.includes(regionLower) ||
          addressLower.includes('нур-султан') ||
          addressLower.includes('астана') ||
          addressLower.includes('алматы') ||
          addressLower.includes('шымкент')) {
        return address;
      } else {
        return `${regionName}, ${address}`;
      }
    }

    return address || regionName || 'Адрес не указан';
  }, [companyData]);



  // Функция для создания заглушки карты
  const createMapPlaceholder = useCallback((container) => {
    const displayAddress = getDisplayAddress();

    if (container) {
      container.innerHTML = `
        <div style="
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
          background: #f0f0f0;
          color: #666;
          font-size: 14px;
          text-align: center;
          padding: 20px;
          border-radius: 8px;
        ">
          <div>
            <div style="margin-bottom: 8px; font-size: 24px;">📍</div>
            <div>${displayAddress}</div>
          </div>
        </div>
      `;
    }
  }, [getDisplayAddress]);

  useEffect(() => {
    const containerId = containerIdRef.current;
    let map = null;

    const initMap = async () => {
      const container = document.getElementById(containerId);

      if (!container) {
        console.warn('Контейнер карты не найден');
        return;
      }

      if (typeof window !== 'undefined' && window.mapgl) {
        try {
          // Определяем адрес для карты: ActualAddress -> LegalAddress
          const address = companyData?.ActualAddress || companyData?.LegalAddress;
          const regionId = companyData?.RegionId;

          // Сначала используем fallback координаты для быстрого отображения карты
          const fallbackCoords = getCoordinatesFromAddress(address, regionId);

          // Создаем карту с fallback координатами
          map = new window.mapgl.Map(containerId, {
            center: fallbackCoords,
            zoom: 13,
            key: 'a1220568-7eca-457e-8dbf-be894c8fa7e6'
          });

          mapInstanceRef.current = map;

          // Добавляем временный маркер
          let marker = null;
          if (fallbackCoords && window.mapgl.Marker) {
            try {
              marker = new window.mapgl.Marker(map, {
                coordinates: fallbackCoords
              });
            } catch (markerError) {
              console.warn('Не удалось добавить временный маркер:', markerError);
            }
          }

          // Асинхронно получаем точные координаты через геокодирование
          if (address) {
            try {
              const exactCoords = await geocodeAddress(address, regionId);

              // Если получили более точные координаты, обновляем карту
              if (exactCoords && (exactCoords[0] !== fallbackCoords[0] || exactCoords[1] !== fallbackCoords[1])) {
                map.setCenter(exactCoords);

                // Удаляем старый маркер и добавляем новый
                if (marker) {
                  try {
                    marker.destroy();
                  } catch (e) {
                    console.warn('Не удалось удалить старый маркер:', e);
                  }
                }

                if (window.mapgl.Marker) {
                  try {
                    new window.mapgl.Marker(map, {
                      coordinates: exactCoords
                    });
                  } catch (markerError) {
                    console.warn('Не удалось добавить точный маркер:', markerError);
                  }
                }
              }
            } catch (geocodeError) {
              console.warn('Ошибка точного геокодирования:', geocodeError);
              // Оставляем fallback координаты и маркер
            }
          }

        } catch (error) {
          console.error('Ошибка инициализации карты 2ГИС:', error);
          createMapPlaceholder(container);
        }
      } else {
        // API не загружен, показываем заглушку
        createMapPlaceholder(container);
      }
    };

    // Инициализируем карту
    if (typeof window !== 'undefined') {
      if (window.mapgl) {
        // API уже загружен
        setTimeout(initMap, 0);
      } else {
        // Ждем загрузки API
        let attempts = 0;
        const maxAttempts = 100; // 10 секунд
        
        const checkMapGL = setInterval(() => {
          attempts++;
          
          if (window.mapgl) {
            clearInterval(checkMapGL);
            initMap();
          } else if (attempts >= maxAttempts) {
            clearInterval(checkMapGL);
            // Показываем заглушку, если API не загрузился
            const container = document.getElementById(containerId);
            createMapPlaceholder(container);
          }
        }, 100);
      }
    }

    // Cleanup при размонтировании компонента
    return () => {
      if (mapInstanceRef.current) {
        try {
          mapInstanceRef.current.destroy();
          mapInstanceRef.current = null;
        } catch (error) {
          console.error('Ошибка при уничтожении карты:', error);
        }
      }
    };
  }, [companyData, getCoordinatesFromAddress, geocodeAddress, createMapPlaceholder]);

  return (
    <div 
      className={className}
      style={{
        width: '100%',
        height: '200px',
        background: '#f0f0f0',
        borderRadius: '8px',
        overflow: 'hidden',
        ...style
      }}
    >
      <MapWrapper containerId={containerIdRef.current} />
    </div>
  );
};

export default Map2GIS;
