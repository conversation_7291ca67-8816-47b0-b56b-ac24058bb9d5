"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import styled from "styled-components";
import { BreadcrumbSchema } from "./StructuredData";

const BreadcrumbContainer = styled.nav`
  display: flex;
  align-items: center;
  margin-top: 24px;
  margin-left: 50px;
  font-size: 14px;
  color: #666;

  a {
    color: #666;
    text-decoration: none;

    &:hover {
      color: #0066cc;
    }
  }

  svg {
    margin: 0 8px;
    width: 16px;
    height: 16px;
  }

  @media (max-width: 768px) {
    margin-left: 20px;
  }
`;

const BreadcrumbList = styled.ol`
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
  flex-wrap: wrap;
`;

const BreadcrumbItem = styled.li`
  display: flex;
  align-items: center;

  &:not(:last-child)::after {
    content: "›";
    margin: 0 8px;
    color: #999;
    font-weight: bold;
  }
`;

const BreadcrumbLink = styled(Link)`
  color: #666;
  text-decoration: none;

  &:hover {
    color: #0066cc;
  }
`;

const BreadcrumbText = styled.span`
  color: #666;
`;

// Функция для генерации хлебных крошек на основе URL
const generateBreadcrumbs = (pathname, searchParams = {}) => {
  const breadcrumbs = [{ name: "Главная", url: "https://shop.sadi.kz" }];

  const segments = pathname.split("/").filter(Boolean);

  // Обработка разных типов страниц
  if (segments[0] === "products") {
    if (segments[1] === "page") {
      // Каталог товаров
      breadcrumbs.push({
        name: searchParams.name
          ? `Поиск: ${searchParams.name}`
          : "Каталог товаров",
        url: `https://shop.sadi.kz/products/page/1`,
      });

      if (segments[2] && segments[2] !== "1") {
        breadcrumbs.push({
          name: `Страница ${segments[2]}`,
          url: `https://shop.sadi.kz/products/page/${segments[2]}`,
        });
      }
    } else if (segments[1] === "category") {
      // Категория товаров
      breadcrumbs.push({
        name: "Каталог товаров",
        url: "https://shop.sadi.kz/products/page/1",
      });
      breadcrumbs.push({
        name: `Категория ${segments[2]}`,
        url: `https://shop.sadi.kz/products/category/${segments[2]}/page/1`,
      });

      if (segments[4] && segments[4] !== "1") {
        breadcrumbs.push({
          name: `Страница ${segments[4]}`,
          url: `https://shop.sadi.kz/products/category/${segments[2]}/page/${segments[4]}`,
        });
      }
    }
  } else if (segments[0] === "product") {
    // Страница товара
    breadcrumbs.push({
      name: "Каталог товаров",
      url: "https://shop.sadi.kz/products/page/1",
    });
    breadcrumbs.push({
      name: `Товар ${segments[1]}`,
      url: `https://shop.sadi.kz/product/${segments[1]}`,
    });
  } else if (segments[0] === "create-tender") {
    breadcrumbs.push({
      name: "Создать тендер",
      url: "https://shop.sadi.kz/create-tender",
    });
  } else if (segments[0] === "find-tender") {
    breadcrumbs.push({
      name: "Найти тендер",
      url: "https://shop.sadi.kz/find-tender",
    });
  } else if (segments[0] === "about") {
    breadcrumbs.push({
      name: "О компании",
      url: "https://shop.sadi.kz/about",
    });
  }

  return breadcrumbs;
};

export default function Breadcrumbs({ customBreadcrumbs, searchParams }) {
  const pathname = usePathname();

  // Используем кастомные хлебные крошки или генерируем автоматически
  const breadcrumbs =
    customBreadcrumbs || generateBreadcrumbs(pathname, searchParams);

  // Не показываем хлебные крошки на главной странице
  if (pathname === "/" || breadcrumbs.length <= 1) {
    return null;
  }

  return (
    <>
      <BreadcrumbSchema items={breadcrumbs} />
      <BreadcrumbContainer>
        <BreadcrumbList itemScope itemType="https://schema.org/BreadcrumbList">
          {breadcrumbs.map((crumb, index) => (
            <BreadcrumbItem
              key={index}
              itemProp="itemListElement"
              itemScope
              itemType="https://schema.org/ListItem"
            >
              <meta itemProp="position" content={index + 1} />
              {index === breadcrumbs.length - 1 ? (
                <BreadcrumbText itemProp="name">{crumb.name}</BreadcrumbText>
              ) : (
                <BreadcrumbLink href={crumb.url} itemProp="item">
                  <span itemProp="name">{crumb.name}</span>
                </BreadcrumbLink>
              )}
            </BreadcrumbItem>
          ))}
        </BreadcrumbList>
      </BreadcrumbContainer>
    </>
  );
}

// Экспорт функции для использования в других компонентах
export { generateBreadcrumbs };
