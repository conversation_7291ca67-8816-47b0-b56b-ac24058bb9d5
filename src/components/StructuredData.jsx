// Компонент для генерации структурированных данных Schema.org

// Структурированные данные для организации
export const OrganizationSchema = () => {
  const schema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: "SADI Shop",
    alternateName: ["САДИ Шоп", "shop.sadi.kz"],
    url: "https://shop.sadi.kz",
    logo: "https://shop.sadi.kz/favicon.ico",
    description: "Интернет-магазин строительных материалов в Казахстане",
    address: {
      "@type": "PostalAddress",
      addressCountry: "KZ",
      addressRegion: "Астана",
      addressLocality: "Астана",
    },
    contactPoint: {
      "@type": "ContactPoint",
      contactType: "customer service",
      availableLanguage: ["Russian", "Kazakh"],
    },
    sameAs: ["https://sadi.kz"],
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  );
};

// Структурированные данные для товара
export const ProductSchema = ({ product, suppliers = [] }) => {
  const schema = {
    "@context": "https://schema.org",
    "@type": "Product",
    name: product.Name,
    description:
      product.Description || `${product.Name} - строительный материал`,
    sku: product.MaterialId,
    mpn: product.MaterialId,
    image: "https://shop.sadi.kz/images/favicon.ico",
    brand: {
      "@type": "Brand",
      name: "SADI Shop",
    },
    category: "Строительные материалы",
    offers:
      suppliers.length > 0
        ? suppliers.map((supplier) => ({
            "@type": "Offer",
            price: supplier.RetailPrice || supplier.TradePrice,
            priceCurrency: "KZT",
            availability: "https://schema.org/InStock",
            seller: {
              "@type": "Organization",
              name: supplier.CreatedBy || "SADI Shop",
            },
            priceValidUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
              .toISOString()
              .split("T")[0],
          }))
        : [
            {
              "@type": "Offer",
              price: product.RetailPrice || 0,
              priceCurrency: "KZT",
              availability: "https://schema.org/InStock",
              seller: {
                "@type": "Organization",
                name: "SADI Shop",
              },
            },
          ],
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: "4.5",
      reviewCount: "10",
    },
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  );
};

// Структурированные данные для хлебных крошек
export const BreadcrumbSchema = ({ items }) => {
  const schema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: items.map((item, index) => ({
      "@type": "ListItem",
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  );
};

// Структурированные данные для каталога товаров
export const ItemListSchema = ({ products, category, page }) => {
  const schema = {
    "@context": "https://schema.org",
    "@type": "ItemList",
    name: `${category || "Каталог"} строительных материалов - страница ${page}`,
    description: `Каталог строительных материалов в SADI Shop - страница ${page}`,
    numberOfItems: products.length,
    itemListElement: products.map((product, index) => ({
      "@type": "ListItem",
      position: index + 1,
      item: {
        "@type": "Product",
        name: product.Name,
        url: `https://shop.sadi.kz/product/${product.MaterialId}`,
        image: "https://shop.sadi.kz/favicon.ico",
        offers: {
          "@type": "Offer",
          price: product.RetailPrice || 0,
          priceCurrency: "KZT",
        },
      },
    })),
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  );
};

// Структурированные данные для FAQ
export const FAQSchema = ({ faqs }) => {
  const schema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: faqs.map((faq) => ({
      "@type": "Question",
      name: faq.question,
      acceptedAnswer: {
        "@type": "Answer",
        text: faq.answer,
      },
    })),
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  );
};

// Структурированные данные для локального бизнеса
export const LocalBusinessSchema = () => {
  const schema = {
    "@context": "https://schema.org",
    "@type": "Store",
    name: "SADI Shop",
    description: "Интернет-магазин строительных материалов в Казахстане",
    url: "https://shop.sadi.kz",
    telephone: "+7 (XXX) XXX-XX-XX",
    address: {
      "@type": "PostalAddress",
      addressCountry: "KZ",
      addressRegion: "Астана",
      addressLocality: "Астана",
    },
    geo: {
      "@type": "GeoCoordinates",
      latitude: "51.1694",
      longitude: "71.4491",
    },
    openingHours: "Mo-Su 00:00-23:59",
    priceRange: "$$",
    servesCuisine: "Строительные материалы",
    paymentAccepted: "Cash, Credit Card, Bank Transfer",
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  );
};
