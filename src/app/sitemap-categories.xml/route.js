import ApiService from "../../services/api.service";

// Базовый URL сайта
const BASE_URL = "https://shop.sadi.kz";

// Генерация sitemap для всех категорий
export async function GET() {
  try {
    // Получаем каталог категорий
    const catalogData = await ApiService.getCatalog();

    const totalUrls = catalogData.length * 3; // По 3 страницы на категорию
    console.log(
      `Sitemap-categories: Генерируем sitemap для ${catalogData.length} категорий (${totalUrls} URL)`
    );

    // Создаем XML для всех категорий
    let xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

    // Добавляем все категории
    catalogData.forEach((category) => {
      if (category.Code) {
        // Добавляем первую страницу категории (основная)
        xml += `
  <url>
    <loc>${BASE_URL}/products/category/${category.Code}/page/1</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>`;

        // Добавляем вторую и третью страницы категории (если есть товары)
        xml += `
  <url>
    <loc>${BASE_URL}/products/category/${category.Code}/page/2</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
  </url>`;

        xml += `
  <url>
    <loc>${BASE_URL}/products/category/${category.Code}/page/3</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>`;
      }
    });

    xml += `
</urlset>`;

    return new Response(xml, {
      headers: {
        "Content-Type": "application/xml",
        "Cache-Control": "public, max-age=3600", // Кэшируем на 1 час
      },
    });
  } catch (error) {
    console.error("Ошибка при генерации sitemap-categories:", error);

    // Возвращаем пустой sitemap в случае ошибки
    const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
</urlset>`;

    return new Response(xml, {
      headers: {
        "Content-Type": "application/xml",
      },
    });
  }
}
