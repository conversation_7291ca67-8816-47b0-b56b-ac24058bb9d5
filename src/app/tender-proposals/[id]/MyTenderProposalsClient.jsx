"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import API_CONFIG from "../../../config/api";
import { useAuth } from "../../../context/AuthContext";

const TenderProposalsContainer = styled.div`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background-color: white;
  min-height: 100vh;
  padding: 24px 20px;
`;
TenderProposalsContainer.displayName = "TenderProposalsContainer";

const ContentContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;
ContentContainer.displayName = "ContentContainer";

const Header = styled.div`
  border-bottom: 1px solid #dfe4e5;
  /* margin-bottom: 24px; */
`;
Header.displayName = "Header";

const HeaderContent = styled.div`
  margin: 0 auto;
  max-width: 1150px;
  display: flex;
  padding: 22px 40px 16px;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;

  @media (max-width: 768px) {
    padding: 16px 20px;
  }
`;
HeaderContent.displayName = "HeaderContent";

const BackButton = styled.button`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background-color: white;
  color: #434a54;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  gap: 8px;
  padding: 8px 20px;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f8f9fa;
  }
`;
BackButton.displayName = "BackButton";

const Title = styled.h1`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  font-size: 36px;
  font-weight: 700;
  line-height: 120%;
  letter-spacing: -0.5px;
  line-height: 1.5;
  color: #434a54;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    font-size: 24px;
  }
`;
Title.displayName = "Title";

const SectionTitle = styled.h2`
  font-size: 24px;
  font-weight: 850;
  color: #434a54;
  margin-top: 42px;
  margin-bottom: 24px;

  &:first-of-type {
    margin-top: 0;
  }
`;
SectionTitle.displayName = "SectionTitle";

const Text = styled.p`
  font-size: 17px;
  font-weight: 400;
  color: #434a54;
  margin-bottom: 16px;
  line-height: 1.5;
`;
Text.displayName = "Text";

const ProductFormCard = styled.div`
  background: white;
  border-radius: 12px;
  padding: 0;
  margin-bottom: 24px;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  overflow: hidden;
`;
ProductFormCard.displayName = "ProductFormCard";

// Секция с информацией о материале (верхняя часть)
const MaterialInfoSection = styled.div`
  background: #f8f9fa;
  padding: 24px;
  border-bottom: 1px solid #e9ecef;
`;
MaterialInfoSection.displayName = "MaterialInfoSection";

// Заголовок секции
const SectionHeader = styled.h4`
  font-size: 22px;
  font-weight: 700;
  color: #434a54;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 12px;
`;
SectionHeader.displayName = "SectionHeader";

// Секция с формой предложения (нижняя часть)
const ProposalFormSection = styled.div`
  background: white;
  padding: 28px;
`;
ProposalFormSection.displayName = "ProposalFormSection";

// Контейнер для информационных элементов
const InfoGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 10px;
  margin-bottom: 10px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
`;
InfoGrid.displayName = "InfoGrid";

// Информационный элемент
const InfoItem = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
`;
InfoItem.displayName = "InfoItem";

// Иконка для информационного элемента
const InfoIcon = styled.div`
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  flex-shrink: 0;

  svg {
    width: 20px;
    height: 20px;
  }
`;
InfoIcon.displayName = "InfoIcon";

// Текст информационного элемента
const InfoText = styled.div`
  font-size: 16px;
  color: #434a54;
  line-height: 1.4;

  strong {
    color: #434a54;
    font-weight: 600;
    font-size: 17px;
  }
`;
InfoText.displayName = "InfoText";

// SVG иконки
const QuantityIcon = () => (
  <svg viewBox="0 0 24 24" fill="currentColor">
    <path d="M3 3h18v2H3V3zm0 4h18v2H3V7zm0 4h18v2H3v-2zm0 4h18v2H3v-2zm0 4h18v2H3v-2z" />
  </svg>
);

const PriceIcon = () => (
  <svg viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1.41 16.09V20h-2.67v-1.93c-1.71-.36-3.16-1.46-3.27-3.4h1.96c.1 1.05.82 1.87 2.65 1.87 1.96 0 2.4-.98 2.4-1.59 0-.83-.44-1.61-2.67-2.14-2.48-.6-4.18-1.62-4.18-3.67 0-1.72 1.39-2.84 3.11-3.21V4h2.67v1.95c1.86.45 2.79 1.86 2.85 3.39H14.3c-.05-1.11-.64-1.87-2.22-1.87-1.5 0-2.4.68-2.4 1.64 0 .84.65 1.39 2.67 1.91s4.18 1.39 4.18 3.91c-.01 1.83-1.38 2.83-3.12 3.16z" />
  </svg>
);

const CheckIcon = () => (
  <svg viewBox="0 0 24 24" fill="currentColor">
    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
  </svg>
);

const DeliveryIcon = () => (
  <svg viewBox="0 0 24 24" fill="currentColor">
    <path d="M20 8h-3V4H3c-1.1 0-2 .9-2 2v11h2c0 1.66 1.34 3 3 3s3-1.34 3-3h6c0 1.66 1.34 3 3 3s3-1.34 3-3h2v-5l-3-4zM6 18.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm13.5-9l1.96 2.5H17V9.5h2.5zm-1.5 9c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5z" />
  </svg>
);

const AttachmentIcon = () => (
  <svg viewBox="0 0 24 24" fill="currentColor">
    <path d="M2 12.5C2 9.46 4.46 7 7.5 7H18c2.21 0 4 1.79 4 4s-1.79 4-4 4H9.5C8.12 15 7 13.88 7 12.5S8.12 10 9.5 10H17v2H9.41c-.55 0-.55 1 0 1H18c1.1 0 2-.9 2-2s-.9-2-2-2H7.5C5.57 9 4 10.57 4 12.5S5.57 16 7.5 16H17v2H7.5C4.46 18 2 15.54 2 12.5z" />
  </svg>
);

const BoxIcon = () => (
  <svg viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2l3 3h4v14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2V5h4l3-3zm0 3L9 8v11h6V8l-3-3z" />
  </svg>
);

const ProposalIcon = () => (
  <svg viewBox="0 0 24 24" fill="currentColor">
    <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z" />
  </svg>
);

// Стили для модального окна галереи
const GalleryModal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
`;
GalleryModal.displayName = "GalleryModal";

const GalleryContainer = styled.div`
  position: relative;
  max-width: 90%;
  max-height: 90%;
  display: flex;
  align-items: center;
  justify-content: center;
`;
GalleryContainer.displayName = "GalleryContainer";

const GalleryImage = styled.img`
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
  border-radius: 8px;
`;
GalleryImage.displayName = "GalleryImage";

const GalleryClose = styled.button`
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  font-size: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10001;

  &:hover {
    background: white;
  }
`;
GalleryClose.displayName = "GalleryClose";

const GalleryNav = styled.button`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  font-size: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10001;
  transition: all 0.2s ease;

  &:hover {
    background: white;
    transform: translateY(-50%) scale(1.1);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: translateY(-50%);
  }

  ${(props) => (props.direction === "prev" ? "left: 20px;" : "right: 20px;")}
`;
GalleryNav.displayName = "GalleryNav";

const GalleryCounter = styled.div`
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
`;
GalleryCounter.displayName = "GalleryCounter";

const GalleryImageName = styled.div`
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;
GalleryImageName.displayName = "GalleryImageName";

const ProductInfo = styled.div`
  flex: 1;
`;
ProductInfo.displayName = "ProductInfo";

// Стили для фотографий материалов
const ProductHeader = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
`;
ProductHeader.displayName = "ProductHeader";

const MaterialImagesContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;
MaterialImagesContainer.displayName = "MaterialImagesContainer";

// Новый контейнер для одного изображения с overlay
const SingleImageContainer = styled.div`
  position: relative;
  width: 120px;
  height: 120px;
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #e0e0e0;
  transition: all 0.2s ease;

  &:hover {
    border-color: #007bff;
    transform: scale(1.02);
  }
`;
SingleImageContainer.displayName = "SingleImageContainer";

// Основное изображение
const MainImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;
MainImage.displayName = "MainImage";

// Overlay для множественных изображений
const ImageOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  font-weight: 700;
  backdrop-filter: blur(1px);
`;
ImageOverlay.displayName = "ImageOverlay";

const NoImagePlaceholder = styled.div`
  width: 120px;
  height: 120px;
  background-color: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #6c757d;
  text-align: center;
  flex-shrink: 0;
`;
NoImagePlaceholder.displayName = "NoImagePlaceholder";

const ProductTitleContainer = styled.div`
  flex: 1;
`;
ProductTitleContainer.displayName = "ProductTitleContainer";

const ProductId = styled.div`
  font-size: 17px;
  color: #969ea7;
  margin-bottom: 10px;
`;
ProductId.displayName = "ProductId";

const ProductTitle = styled.h3`
  font-size: 26px;
  font-weight: 700;
  color: #434a54;
  line-height: 1.3;
  margin-bottom: 12px;
`;
ProductTitle.displayName = "ProductTitle";

const Label = styled.div`
  font-size: 16px;
  font-weight: 400;
  line-height: 1.4;
  color: #6c757d;
  margin-bottom: 12px;

  @media (max-width: 768px) {
    font-size: 18px;
  }
`;
Label.displayName = "Label";

const ViewTenderButton = styled.button`
  background-color: transparent;
  border: 2px solid #0066cc;
  color: #0066cc;
  padding: 12px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;

  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f0f8ff;
  }
`;
ViewTenderButton.displayName = "ViewTenderButton";

const Input = styled.input`
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  height: 36px;
  padding-right: 40px;

  &:focus {
    border-color: #0066cc;
    outline: none;
  }
`;
Input.displayName = "Input";

const FormRow = styled.div`
  display: flex;
  gap: 24px;
  padding: 10px 0;
  margin-bottom: 16px;
  align-items: flex-end;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
`;
FormRow.displayName = "FormRow";

const SmallFormGroup = styled.div`
  display: flex;
  flex-direction: column;
  max-width: 200px;
  position: relative;
`;
SmallFormGroup.displayName = "SmallFormGroup";

const TextArea = styled.textarea`
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-height: 75px;
  width: 100%;
  resize: vertical;
  color: #434a54;
  margin-bottom: 8px;

  &:focus {
    outline: none;
    border-color: #0066cc;
  }
`;
TextArea.displayName = "TextArea";

const ActionButtonContainer = styled.div`
  display: flex;
  gap: 0;
  height: 36px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #0066cc;
  width: fit-content;
`;
ActionButtonContainer.displayName = "ActionButtonContainer";

const ActionButton = styled.button`
  background-color: ${(props) => (props.active ? "#0066cc" : "#f8f9fa")};
  color: ${(props) => (props.active ? "white" : "#434a54")};
  border: none;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  white-space: nowrap;
  height: 36px;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => (props.active ? "#0056b3" : "#e9ecef")};
  }
`;
ActionButton.displayName = "ActionButton";

const DateButtonViewTenderButtonContainer = styled.div`
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
`;
DateButtonViewTenderButtonContainer.displayName =
  "DateButtonViewTenderButtonContainer";

const DateButton = styled.button`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  background: white;
  border: 2px solid #0066cc;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  color: #0066cc;
  font-weight: 600;
  margin-top: 0px;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f0f8ff;
  }
`;
DateButton.displayName = "DateButton";

const NoTenderMessage = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: #666;
  font-size: 16px;
`;
NoTenderMessage.displayName = "NoTenderMessage";

const LoadingMessage = styled.div`
  text-align: center;
  padding: 40px;
  font-size: 18px;
  color: #666;
`;
LoadingMessage.displayName = "LoadingMessage";

const ErrorMessage = styled.div`
  text-align: center;
  padding: 40px;
  font-size: 16px;
  color: #dc3545;
`;
ErrorMessage.displayName = "ErrorMessage";

// Стили для уведомлений
const SuccessNotification = styled.div`
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #28a745;
  color: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 16px;
  min-width: 300px;
  max-width: 500px;
  animation: slideDown 0.3s ease-out;

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateX(-50%) translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
  }

  @media (max-width: 768px) {
    left: 10px;
    right: 10px;
    transform: none;
    font-size: 14px;
    padding: 12px 20px;
    max-width: 95%;
  }
`;
SuccessNotification.displayName = "SuccessNotification";

const SuccessNotificationText = styled.span`
  flex: 1;
`;
SuccessNotificationText.displayName = "SuccessNotificationText";

const SuccessNotificationClose = styled.button`
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 18px;
  padding: 4px 8px;
  border-radius: 4px;

  &:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }
`;
SuccessNotificationClose.displayName = "SuccessNotificationClose";

const ErrorNotification = styled.div`
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #dc3545;
  color: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 16px;
  min-width: 300px;
  max-width: 500px;
  animation: slideDown 0.3s ease-out;

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateX(-50%) translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
  }

  @media (max-width: 768px) {
    left: 10px;
    right: 10px;
    transform: none;
    font-size: 14px;
    padding: 12px 20px;
    max-width: 95%;
  }
`;
ErrorNotification.displayName = "ErrorNotification";

const MyTenderProposalsClient = ({ tenderId }) => {
  const router = useRouter();
  const { isAuthenticated } = useAuth();
  const [tenderInfo, setTenderInfo] = useState(null);
  const [tenderDetails, setTenderDetails] = useState(null); // Детальная информация о тендере
  const [materialPhotos, setMaterialPhotos] = useState({}); // Фотографии материалов по purchReqLineId
  const [materialImages, setMaterialImages] = useState({}); // Фотографии материалов по materialId
  const [selectedImage, setSelectedImage] = useState(null); // Для модального окна с полноразмерным изображением
  const [galleryImages, setGalleryImages] = useState([]); // Массив изображений для галереи
  const [currentImageIndex, setCurrentImageIndex] = useState(0); // Текущий индекс изображения в галерее
  const [isGalleryOpen, setIsGalleryOpen] = useState(false); // Состояние открытия галереи
  const [materials, setMaterials] = useState([]);
  const [priceProposals, setPriceProposals] = useState({}); // Хранит предложения по каждому материалу
  const [companyNames, setCompanyNames] = useState({}); // Хранит названия компаний
  const [proposalPhotos, setProposalPhotos] = useState({}); // Хранит фотографии предложений по purchReqPriceId
  const [companyEmails, setCompanyEmails] = useState({}); // Хранит email компаний

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [sendingEmail, setSendingEmail] = useState(false);
  const [showSuccessNotification, setShowSuccessNotification] = useState(false);
  const [showErrorNotification, setShowErrorNotification] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState("");

  const purchReqId = tenderId;

  // Функция для получения материалов тендера
  const fetchTenderMaterials = async () => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqLines?purchReqId=${purchReqId}`
      );
      if (response.ok) {
        const data = await response.json();
        const materialsData = Array.isArray(data) ? data : [];
        setMaterials(materialsData);
        setTenderInfo(data); // Устанавливаем также информацию о тендере
        return materialsData;
      } else {
        throw new Error("Не удалось загрузить материалы тендера");
      }
    } catch (error) {
      console.error("Ошибка при загрузке материалов:", error);
      setError(error.message);
      return [];
    }
  };

  // Функция для получения ценовых предложений по материалу
  const fetchPriceProposals = async (purchReqLineId) => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqPrices?purchReqLineId=${purchReqLineId}`
      );
      if (response.ok) {
        const data = await response.json();
        return data;
      } else {
        console.warn(
          `Не удалось загрузить предложения для материала ${purchReqLineId}`
        );
        return null;
      }
    } catch (error) {
      console.error("Ошибка при загрузке ценовых предложений:", error);
      return null;
    }
  };

  // Функция для загрузки названий компаний и их email
  const fetchCompanyNames = async (companyIds) => {
    if (!companyIds || companyIds.length === 0) {
      return;
    }

    try {
      // Создаем массив промисов для всех запросов к компаниям
      const promises = companyIds.map(async (companyId) => {
        const response = await fetch(
          `${API_CONFIG.BASE_URL}/api/Companies/${companyId}`
        );

        if (!response.ok) {
          console.warn(
            `Ошибка загрузки компании ${companyId}: ${response.status}`
          );
          return {
            companyId,
            companyName: "Неизвестная компания",
            email: "",
          };
        }

        const companyData = await response.json();
        return {
          companyId,
          companyName: companyData.CompanyName || "Неизвестная компания",
          email: companyData.Email || "",
        };
      });

      // Ждем выполнения всех запросов
      const results = await Promise.all(promises);

      // Обновляем состояние с названиями компаний и email
      const newCompanyNames = {};
      const newCompanyEmails = {};
      results.forEach(({ companyId, companyName, email }) => {
        newCompanyNames[companyId] = companyName;
        newCompanyEmails[companyId] = email;
      });

      setCompanyNames((prev) => ({ ...prev, ...newCompanyNames }));
      setCompanyEmails((prev) => ({ ...prev, ...newCompanyEmails }));
    } catch (err) {
      console.error("Ошибка при загрузке названий компаний:", err);
    }
  };

  // Функция для загрузки фотографий материала тендера
  const fetchMaterialPhotos = async (purchReqId, purchReqLineId) => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqTablePhotos?purchReqId=${purchReqId}&purchReqLineId=${purchReqLineId}`
      );
      if (response.ok) {
        const data = await response.json();
        return Array.isArray(data) ? data : [];
      } else {
        console.warn(
          `Не удалось загрузить фотографии материала ${purchReqLineId}`
        );
        return [];
      }
    } catch (error) {
      console.error("Ошибка при загрузке фотографий материала:", error);
      return [];
    }
  };

  // Функция для загрузки фотографий материалов по materialIds
  const fetchMaterialImages = async (materialIds) => {
    try {
      if (!materialIds || materialIds.length === 0) {
        return {};
      }

      // Формируем query параметры для materialIds
      const queryParams = materialIds
        .map((id) => `materialIds=${id}`)
        .join("&");
      const url = `${API_CONFIG.BASE_URL}/api/Adverts/Files/ByMaterial?${queryParams}&fileType=.png.jpg.jpeg`;

      console.log("Загружаем фотографии материалов:", url);

      const response = await fetch(url);
      if (response.ok) {
        const data = await response.json();
        console.log("Получены фотографии материалов:", data);
        return data;
      } else {
        console.warn(
          `Не удалось загрузить фотографии материалов: ${response.status}`
        );
        return {};
      }
    } catch (error) {
      console.error("Ошибка при загрузке фотографий материалов:", error);
      return {};
    }
  };

  // Функция для загрузки фотографий предложения
  const fetchProposalPhotos = async (purchReqPriceId) => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqPricePhotos?purchReqPriceId=${purchReqPriceId}`
      );
      if (response.ok) {
        const data = await response.json();
        return Array.isArray(data) ? data : [];
      } else {
        console.warn(
          `Не удалось загрузить фотографии предложения ${purchReqPriceId}`
        );
        return [];
      }
    } catch (error) {
      console.error("Ошибка при загрузке фотографий предложения:", error);
      return [];
    }
  };

  // Функция для скачивания файла
  const handleDownloadFile = (fileUrl, fileName) => {
    try {
      // Создаем временную ссылку для скачивания
      const link = document.createElement("a");
      link.href = fileUrl;
      link.download = fileName || "file";
      link.target = "_blank";

      // Добавляем ссылку в DOM, кликаем и удаляем
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Ошибка при скачивании файла:", error);
      // Fallback - открываем файл в новой вкладке
      window.open(fileUrl, "_blank");
    }
  };

  // Функция для определения типа файла
  const getFileType = (fileName) => {
    if (!fileName) return "unknown";
    const extension = fileName.toLowerCase().split(".").pop();

    const imageTypes = ["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg"];
    const documentTypes = ["pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx"];
    const archiveTypes = ["zip", "rar", "7z", "tar", "gz"];

    if (imageTypes.includes(extension)) return "image";
    if (documentTypes.includes(extension)) return "document";
    if (archiveTypes.includes(extension)) return "archive";
    return "unknown";
  };

  // Функция для получения иконки файла
  const getFileIcon = (fileName) => {
    const extension = fileName ? fileName.toLowerCase().split(".").pop() : "";

    switch (extension) {
      case "pdf":
        return "📄";
      case "doc":
      case "docx":
        return "📝";
      case "xls":
      case "xlsx":
        return "📊";
      case "ppt":
      case "pptx":
        return "📋";
      case "zip":
      case "rar":
      case "7z":
        return "📦";
      case "txt":
        return "📃";
      default:
        return "📎";
    }
  };

  // Функция для получения детальной информации о тендере
  const fetchTenderDetails = async (purchReqId) => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqTables/${purchReqId}`
      );
      if (response.ok) {
        const data = await response.json();
        return data;
      } else {
        console.warn(`Не удалось загрузить детали тендера ${purchReqId}`);
        return null;
      }
    } catch (error) {
      console.error("Ошибка при загрузке деталей тендера:", error);
      return null;
    }
  };

  // Функции для работы с галереей
  const openGallery = (images, startIndex = 0) => {
    const galleryData = images.map((image) => ({
      src: `${API_CONFIG.BASE_URL}/api/Adverts/File?certificateId=${image.CertificateId}`,
      alt: image.FileName,
      title: image.FileName,
    }));
    setGalleryImages(galleryData);
    setCurrentImageIndex(startIndex);
    setIsGalleryOpen(true);
  };

  const closeGallery = () => {
    setIsGalleryOpen(false);
    setGalleryImages([]);
    setCurrentImageIndex(0);
  };

  const nextImage = () => {
    setCurrentImageIndex((prev) =>
      prev < galleryImages.length - 1 ? prev + 1 : 0
    );
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) =>
      prev > 0 ? prev - 1 : galleryImages.length - 1
    );
  };

  // Обработка клавиш для навигации по галерее
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (!isGalleryOpen) return;

      switch (e.key) {
        case "ArrowLeft":
          prevImage();
          break;
        case "ArrowRight":
          nextImage();
          break;
        case "Escape":
          closeGallery();
          break;
        default:
          break;
      }
    };

    document.addEventListener("keydown", handleKeyPress);
    return () => document.removeEventListener("keydown", handleKeyPress);
  }, [isGalleryOpen, galleryImages.length]);

  // Компонент для отображения миниатюр фотографий материала
  const MaterialImageThumbnails = ({ materialId }) => {
    const images = materialImages[materialId] || [];

    const handleImageClick = () => {
      if (images.length > 0) {
        openGallery(images, 0);
      }
    };

    if (images.length === 0) {
      return (
        <MaterialImagesContainer>
          <NoImagePlaceholder>Нет фото</NoImagePlaceholder>
        </MaterialImagesContainer>
      );
    }

    const firstImage = images[0];
    const remainingCount = images.length - 1;

    return (
      <MaterialImagesContainer>
        <SingleImageContainer onClick={handleImageClick}>
          <MainImage
            src={`${API_CONFIG.BASE_URL}/api/Adverts/File?certificateId=${firstImage.CertificateId}`}
            alt={firstImage.FileName}
            title={firstImage.FileName}
            onError={(e) => {
              e.target.style.display = "none";
            }}
          />
          {remainingCount > 0 && <ImageOverlay>+{remainingCount}</ImageOverlay>}
        </SingleImageContainer>
      </MaterialImagesContainer>
    );
  };

  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/auth");
      return;
    }

    const loadData = async () => {
      if (!purchReqId) {
        setError("ID тендера не указан");
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // Загружаем данные параллельно
        const [tenderDetailsData, materialsData] = await Promise.all([
          fetchTenderDetails(purchReqId),
          fetchTenderMaterials(),
        ]);

        setTenderDetails(tenderDetailsData);

        // Загружаем ценовые предложения для каждого материала
        if (materialsData && materialsData.length > 0) {
          const proposalsPromises = materialsData.map(async (material) => {
            const proposals = await fetchPriceProposals(
              material.PurchReqLineId
            );
            return {
              purchReqLineId: material.PurchReqLineId,
              proposals: proposals,
            };
          });

          const proposalsResults = await Promise.all(proposalsPromises);

          // Создаем объект с предложениями по каждому материалу
          const proposalsData = {};
          const allCompanyIds = new Set();

          proposalsResults.forEach(({ purchReqLineId, proposals }) => {
            proposalsData[purchReqLineId] = proposals;

            // Собираем все CompanyId из предложений
            if (proposals && Array.isArray(proposals)) {
              proposals.forEach((proposal) => {
                if (proposal.CompanyId) {
                  allCompanyIds.add(proposal.CompanyId);
                }
              });
            }
          });

          setPriceProposals(proposalsData);

          // Загружаем названия компаний
          if (allCompanyIds.size > 0) {
            fetchCompanyNames(Array.from(allCompanyIds));
          }

          // Загружаем фотографии для всех предложений
          const allPurchReqPriceIds = new Set();
          proposalsResults.forEach(({ proposals }) => {
            if (proposals && Array.isArray(proposals)) {
              proposals.forEach((proposal) => {
                if (proposal.PurchReqPriceId) {
                  allPurchReqPriceIds.add(proposal.PurchReqPriceId);
                }
              });
            }
          });

          // Загружаем фотографии для каждого предложения
          if (allPurchReqPriceIds.size > 0) {
            const photosPromises = Array.from(allPurchReqPriceIds).map(
              async (purchReqPriceId) => {
                const photos = await fetchProposalPhotos(purchReqPriceId);
                return { purchReqPriceId, photos };
              }
            );

            const photosResults = await Promise.all(photosPromises);
            const photosData = {};
            photosResults.forEach(({ purchReqPriceId, photos }) => {
              photosData[purchReqPriceId] = photos;
            });

            setProposalPhotos(photosData);
          }

          // Загружаем фотографии для каждого материала
          const materialPhotosPromises = materialsData.map(async (material) => {
            const photos = await fetchMaterialPhotos(
              purchReqId,
              material.PurchReqLineId
            );
            return {
              purchReqLineId: material.PurchReqLineId,
              photos: photos,
            };
          });

          const materialPhotosResults = await Promise.all(
            materialPhotosPromises
          );
          const materialPhotosData = {};
          materialPhotosResults.forEach(({ purchReqLineId, photos }) => {
            materialPhotosData[purchReqLineId] = photos;
          });

          setMaterialPhotos(materialPhotosData);

          // Загружаем фотографии материалов по materialIds (новый способ)
          const materialIds = materialsData
            .map((material) => material.MaterialId)
            .filter((id) => id);
          if (materialIds.length > 0) {
            const materialImagesData = await fetchMaterialImages(materialIds);
            setMaterialImages(materialImagesData);
          }
        }
      } catch (err) {
        setError(err.message);
        console.error("Ошибка при загрузке данных:", err);
      } finally {
        setIsLoading(false);
      }
    };

    if (purchReqId) {
      loadData();
    }
  }, [purchReqId, isAuthenticated, router]);

  // Функции для управления уведомлениями
  const showSuccess = (message) => {
    setNotificationMessage(message);
    setShowSuccessNotification(true);
    setTimeout(() => {
      setShowSuccessNotification(false);
    }, 3000);
  };

  const showError = (message) => {
    setNotificationMessage(message);
    setShowErrorNotification(true);
    setTimeout(() => {
      setShowErrorNotification(false);
    }, 3000);
  };

  const handleCloseSuccessNotification = () => {
    setShowSuccessNotification(false);
  };

  const handleCloseErrorNotification = () => {
    setShowErrorNotification(false);
  };

  // Функция для отправки письма о принятии предложения
  const sendAcceptanceEmail = async (proposal, material) => {
    try {
      setSendingEmail(true);

      const companyEmail = companyEmails[proposal.CompanyId];
      if (!companyEmail) {
        showError("Email компании не найден");
        return;
      }

      // Формируем HTML-тело письма
      const emailBody = `
        <div style="font-family: Arial, sans-serif; background: #ffffff; padding: 30px; text-align: center; color: #000;">
          <h2 style="color:#00a0df; margin-top:0;">SADI.KZ</h2>
          <p style="font-size:18px; margin: 10px 0 30px;">Ваше ценовое предложение принято от <a href="https://sadi.kz" style="color:#00a0df; text-decoration: none;">sadi.kz</a></p>
          <h3 style="margin: 20px 0;">Детали тендера</h3>
          <table style="margin: 0 auto; text-align: left; font-size: 16px; border-collapse: collapse; width: 100%; max-width: 600px;">
            <tr style="background-color: #f8f9fa;">
              <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">Название тендера:</td>
              <td style="padding: 12px; border: 1px solid #ddd;">${
                tenderDetails?.PurchReqName || "Не указано"
              }</td>
            </tr>
            <tr>
              <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">Материал:</td>
              <td style="padding: 12px; border: 1px solid #ddd;">${
                material.MaterialName
              }</td>
            </tr>
            <tr style="background-color: #f8f9fa;">
              <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">Количество:</td>
              <td style="padding: 12px; border: 1px solid #ddd;">${
                material.PurchQty
              } ${material.PurchUnit}</td>
            </tr>
            <tr>
              <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">Ваша цена:</td>
              <td style="padding: 12px; border: 1px solid #ddd;">${
                proposal.OfferPrice
              } ₸ за ${material.PurchUnit}</td>
            </tr>
            <tr style="background-color: #f8f9fa;">
              <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">Цена с доставкой:</td>
              <td style="padding: 12px; border: 1px solid #ddd;">${
                proposal.PriceWithDelivery ? "Да" : "Нет"
              }</td>
            </tr>
            <tr>
              <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">Адрес доставки:</td>
              <td style="padding: 12px; border: 1px solid #ddd;">${
                tenderDetails?.DeliveryAddress || "Не указан"
              }</td>
            </tr>
            <tr style="background-color: #f8f9fa;">
              <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">Дата окончания тендера:</td>
              <td style="padding: 12px; border: 1px solid #ddd;">${
                tenderDetails?.PurchEndDate
                  ? new Date(tenderDetails.PurchEndDate).toLocaleDateString(
                      "ru-RU"
                    )
                  : "Не указано"
              }</td>
            </tr>
            <tr>
              <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">Тип предложения:</td>
              <td style="padding: 12px; border: 1px solid #ddd;">${
                proposal.IsOriginal ? "Соответствует запросу" : "Аналог"
              }</td>
            </tr>
            <tr style="background-color: #f8f9fa;">
              <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">Комментарий к предложению:</td>
              <td style="padding: 12px; border: 1px solid #ddd;">${
                proposal.Description || "Не указан"
              }</td>
            </tr>
            ${
              proposalPhotos[proposal.PurchReqPriceId] &&
              proposalPhotos[proposal.PurchReqPriceId].length > 0
                ? `
            <tr>
              <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">Прикрепленные файлы:</td>
              <td style="padding: 12px; border: 1px solid #ddd;">
                ${proposalPhotos[proposal.PurchReqPriceId]
                  .map(
                    (photo) =>
                      `<a href="${photo.FileUrl}" style="color: #00a0df; text-decoration: none; display: block; margin: 2px 0;">${photo.FileName}</a>`
                  )
                  .join("")}
              </td>
            </tr>
            `
                : ""
            }
          </table>
          <p style="margin-top: 30px; font-size: 16px;">
            <a href="https://shop.sadi.kz/tender-proposal/${purchReqId}" style="color: #00a0df; text-decoration: none; font-weight: bold;">
  https://shop.sadi.kz/tender-proposal/${purchReqId}
</a>
          </p>
          <p style="margin-top: 20px; font-size: 16px; color: #666;">
            Спасибо за участие в тендере! Заказчик свяжется с вами для уточнения деталей.
          </p>
        </div>
      `;

      const emailData = {
        Email: companyEmail,
        Title: "Ваше ценовое предложение принято - SADI.KZ",
        Body: emailBody,
      };

      const response = await fetch(`${API_CONFIG.BASE_URL}/api/Mail/Send`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(emailData),
      });

      if (response.ok) {
        showSuccess("Письмо о принятии предложения отправлено!");
      } else {
        throw new Error("Ошибка при отправке письма");
      }
    } catch (error) {
      console.error("Ошибка при отправке письма:", error);
      showError("Ошибка при отправке письма: " + error.message);
    } finally {
      setSendingEmail(false);
    }
  };

  const handleBack = () => {
    router.push("/my-tenders");
  };

  if (isLoading) {
    return (
      <TenderProposalsContainer>
        <ContentContainer>
          <LoadingMessage>Загрузка ценовых предложений...</LoadingMessage>
        </ContentContainer>
      </TenderProposalsContainer>
    );
  }

  if (error) {
    return (
      <TenderProposalsContainer>
        <ContentContainer>
          <ErrorMessage>Ошибка: {error}</ErrorMessage>
        </ContentContainer>
      </TenderProposalsContainer>
    );
  }

  if (!tenderInfo) {
    return (
      <TenderProposalsContainer>
        <ContentContainer>
          <NoTenderMessage>Тендер не найден</NoTenderMessage>
        </ContentContainer>
      </TenderProposalsContainer>
    );
  }

  return (
    <>
      {/* Уведомления */}
      {showSuccessNotification && (
        <SuccessNotification>
          <SuccessNotificationText>
            {notificationMessage}
          </SuccessNotificationText>
          <SuccessNotificationClose onClick={handleCloseSuccessNotification}>
            ×
          </SuccessNotificationClose>
        </SuccessNotification>
      )}

      {showErrorNotification && (
        <ErrorNotification>
          <SuccessNotificationText>
            {notificationMessage}
          </SuccessNotificationText>
          <SuccessNotificationClose onClick={handleCloseErrorNotification}>
            ×
          </SuccessNotificationClose>
        </ErrorNotification>
      )}

      <Header>
        <HeaderContent>
          <BackButton onClick={handleBack}>
            <img
              src="/icons/arrow_back_24px.svg"
              alt="Назад"
              style={{ width: "12px", height: "12px" }}
            />
            Назад к моим тендерам
          </BackButton>
        </HeaderContent>
      </Header>

      <TenderProposalsContainer>
        <ContentContainer>
          <Title>Ценовые предложения по тендеру</Title>

          {/* Информация о тендере */}
          {tenderDetails && (
            <ProductFormCard
              style={{
                marginBottom: "24px",
                backgroundColor: "#f0f8ff",
                padding: "16px",
              }}
            >
              <ProductInfo>
                <ProductTitle>{tenderDetails.PurchReqName}</ProductTitle>
                <Label>
                  Дата окончания:{" "}
                  {tenderDetails.PurchEndDate
                    ? new Date(tenderDetails.PurchEndDate).toLocaleDateString(
                        "ru-RU"
                      )
                    : "Не указано"}
                </Label>
                <Label>
                  Адрес доставки:{" "}
                  <em>{tenderDetails.DeliveryAddress || "Не указан"}</em>
                </Label>
                {tenderDetails.Description && (
                  <Label>Описание: {tenderDetails.Description}</Label>
                )}
              </ProductInfo>
            </ProductFormCard>
          )}

          <SectionTitle>Материалы тендера</SectionTitle>
          {materials.map((material) => {
            const proposalsArray = priceProposals[material.PurchReqLineId];

            return (
              <div key={material.MaterialId}>
                {/* Информация о материале */}
                <ProductFormCard>
                  <MaterialInfoSection>
                    <SectionHeader>Детали заказа</SectionHeader>

                    {/* Фотографии материала и название */}
                    <ProductHeader>
                      <MaterialImageThumbnails
                        materialId={material.MaterialId}
                      />
                      <ProductTitleContainer>
                        <ProductTitle style={{ marginBottom: "8px" }}>
                          {material.MaterialName}
                        </ProductTitle>
                        <ProductId
                          style={{ fontSize: "16px", color: "#6c757d" }}
                        >
                          ID: {material.MaterialId}
                        </ProductId>
                      </ProductTitleContainer>
                    </ProductHeader>

                    {/* Информация о материале в виде сетки */}
                    <InfoGrid>
                      <InfoItem>
                        <InfoIcon>
                          <QuantityIcon />
                        </InfoIcon>
                        <InfoText>
                          <strong>{material.PurchQty}</strong>{" "}
                          {material.PurchUnit}
                        </InfoText>
                      </InfoItem>

                      <InfoItem>
                        <InfoIcon>
                          <PriceIcon />
                        </InfoIcon>
                        <InfoText>
                          <strong>
                            {material.PurchOpenPrice
                              ? `${material.PurchOpenPrice} ₸ за ${material.PurchUnit}`
                              : "Цена не указана"}
                          </strong>
                        </InfoText>
                      </InfoItem>
                    </InfoGrid>

                    {/* Комментарии заказчика */}
                    {material.Description && (
                      <div
                        style={{
                          marginTop: "16px",
                          padding: "12px",
                          backgroundColor: "#e3f2fd",
                          borderRadius: "8px",
                          borderLeft: "4px solid #2196f3",
                        }}
                      >
                        <InfoText>
                          <strong>Комментарии заказчика:</strong>
                          <br />
                          <em>{material.Description}</em>
                        </InfoText>
                      </div>
                    )}

                    {/* Прикрепленные файлы к материалу */}
                    {materialPhotos[material.PurchReqLineId] &&
                      materialPhotos[material.PurchReqLineId].length > 0 && (
                        <div style={{ marginBottom: "16px" }}>
                          <Label
                            style={{
                              fontSize: "14px",
                              color: "#666",
                              marginBottom: "8px",
                              marginTop: "8px",
                            }}
                          >
                            Прикрепленные файлы к материалу:
                          </Label>
                          <div
                            style={{
                              display: "flex",
                              flexWrap: "wrap",
                              gap: "12px",
                            }}
                          >
                            {materialPhotos[material.PurchReqLineId].map(
                              (photo, photoIndex) => {
                                const fileType = getFileType(photo.FileName);
                                const isImage = fileType === "image";

                                return (
                                  <div
                                    key={photo.PurchReqTableFotoId}
                                    style={{
                                      position: "relative",
                                      cursor: "pointer",
                                    }}
                                    onClick={() =>
                                      handleDownloadFile(
                                        photo.FileUrl,
                                        photo.FileName
                                      )
                                    }
                                    title={`Скачать ${photo.FileName}`}
                                  >
                                    {isImage ? (
                                      <img
                                        src={photo.FileUrl}
                                        alt={`Фото материала ${photoIndex + 1}`}
                                        style={{
                                          width: "150px",
                                          height: "150px",
                                          objectFit: "cover",
                                          borderRadius: "4px",
                                          border: "1px solid #ddd",
                                          transition: "opacity 0.2s",
                                        }}
                                        onError={(e) => {
                                          e.target.style.display = "none";
                                        }}
                                        onMouseEnter={(e) => {
                                          e.target.style.opacity = "0.8";
                                        }}
                                        onMouseLeave={(e) => {
                                          e.target.style.opacity = "1";
                                        }}
                                      />
                                    ) : (
                                      <div
                                        style={{
                                          width: "150px",
                                          height: "150px",
                                          borderRadius: "4px",
                                          border: "1px solid #ddd",
                                          display: "flex",
                                          flexDirection: "column",
                                          alignItems: "center",
                                          justifyContent: "center",
                                          backgroundColor: "#f8f9fa",
                                          transition: "opacity 0.2s",
                                        }}
                                        onMouseEnter={(e) => {
                                          e.target.style.opacity = "0.8";
                                        }}
                                        onMouseLeave={(e) => {
                                          e.target.style.opacity = "1";
                                        }}
                                      >
                                        <div
                                          style={{
                                            fontSize: "48px",
                                            marginBottom: "8px",
                                          }}
                                        >
                                          {getFileIcon(photo.FileName)}
                                        </div>
                                        <div
                                          style={{
                                            fontSize: "10px",
                                            textAlign: "center",
                                            padding: "0 4px",
                                            color: "#666",
                                            wordBreak: "break-word",
                                          }}
                                        >
                                          {photo.FileName}
                                        </div>
                                      </div>
                                    )}

                                    {isImage && (
                                      <div
                                        style={{
                                          position: "absolute",
                                          bottom: "4px",
                                          left: "4px",
                                          right: "4px",
                                          background: "rgba(0,0,0,0.7)",
                                          color: "white",
                                          fontSize: "10px",
                                          padding: "2px 4px",
                                          borderRadius: "2px",
                                          textAlign: "center",
                                          pointerEvents: "none",
                                        }}
                                      >
                                        {photo.FileName}
                                      </div>
                                    )}

                                    {/* Иконка скачивания */}
                                    <div
                                      style={{
                                        position: "absolute",
                                        top: "4px",
                                        right: "4px",
                                        background: "rgba(0,0,0,0.7)",
                                        color: "white",
                                        borderRadius: "50%",
                                        width: "24px",
                                        height: "24px",
                                        display: "flex",
                                        alignItems: "center",
                                        justifyContent: "center",
                                        fontSize: "12px",
                                        pointerEvents: "none",
                                      }}
                                    >
                                      ⬇
                                    </div>
                                  </div>
                                );
                              }
                            )}
                          </div>
                        </div>
                      )}
                  </MaterialInfoSection>
                </ProductFormCard>

                {/* Ценовые предложения для этого материала */}
                {proposalsArray && proposalsArray.length > 0 ? (
                  proposalsArray.map((proposal, proposalIndex) => (
                    <ProductFormCard
                      key={`${material.MaterialId}-${proposalIndex}`}
                      style={{ marginBottom: "20px", padding: "20px" }}
                    >
                      <div
                        style={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                          marginBottom: "16px",
                          padding: "8px 12px",
                          backgroundColor: "#e8f4fd",
                          borderRadius: "4px",
                        }}
                      >
                        <Label
                          style={{
                            fontSize: "16px",
                            fontWeight: "600",
                            color: "#0066cc",
                            margin: 0,
                          }}
                        >
                          Ценовое предложение №{proposalIndex + 1}
                        </Label>
                        <Label
                          style={{
                            fontSize: "14px",
                            color: "#666",
                            margin: 0,
                          }}
                        >
                          от:{" "}
                          {companyNames[proposal.CompanyId] ||
                            "Загрузка компании..."}
                        </Label>
                      </div>

                      {/* Фотографии предложения */}
                      {proposalPhotos[proposal.PurchReqPriceId] &&
                        proposalPhotos[proposal.PurchReqPriceId].length > 0 && (
                          <div style={{ marginBottom: "16px" }}>
                            <Label
                              style={{
                                fontSize: "14px",
                                color: "#666",
                                marginBottom: "8px",
                              }}
                            >
                              Прикрепленные файлы к предложению:
                            </Label>
                            <div
                              style={{
                                display: "flex",
                                flexWrap: "wrap",
                                gap: "12px",
                              }}
                            >
                              {proposalPhotos[proposal.PurchReqPriceId].map(
                                (photo, photoIndex) => {
                                  const fileType = getFileType(photo.FileName);
                                  const isImage = fileType === "image";

                                  return (
                                    <div
                                      key={photo.PurchReqPricePhotoId}
                                      style={{
                                        position: "relative",
                                        cursor: "pointer",
                                      }}
                                      onClick={() =>
                                        handleDownloadFile(
                                          photo.FileUrl,
                                          photo.FileName
                                        )
                                      }
                                      title={`Скачать ${photo.FileName}`}
                                    >
                                      {isImage ? (
                                        <img
                                          src={photo.FileUrl}
                                          alt={`Фото предложения ${
                                            photoIndex + 1
                                          }`}
                                          style={{
                                            width: "150px",
                                            height: "150px",
                                            objectFit: "cover",
                                            borderRadius: "4px",
                                            border: "1px solid #ddd",
                                            transition: "opacity 0.2s",
                                          }}
                                          onError={(e) => {
                                            e.target.style.display = "none";
                                          }}
                                          onMouseEnter={(e) => {
                                            e.target.style.opacity = "0.8";
                                          }}
                                          onMouseLeave={(e) => {
                                            e.target.style.opacity = "1";
                                          }}
                                        />
                                      ) : (
                                        <div
                                          style={{
                                            width: "150px",
                                            height: "150px",
                                            borderRadius: "4px",
                                            border: "1px solid #ddd",
                                            display: "flex",
                                            flexDirection: "column",
                                            alignItems: "center",
                                            justifyContent: "center",
                                            backgroundColor: "#f8f9fa",
                                            transition: "opacity 0.2s",
                                          }}
                                          onMouseEnter={(e) => {
                                            e.target.style.opacity = "0.8";
                                          }}
                                          onMouseLeave={(e) => {
                                            e.target.style.opacity = "1";
                                          }}
                                        >
                                          <div
                                            style={{
                                              fontSize: "48px",
                                              marginBottom: "8px",
                                            }}
                                          >
                                            {getFileIcon(photo.FileName)}
                                          </div>
                                          <div
                                            style={{
                                              fontSize: "10px",
                                              textAlign: "center",
                                              padding: "0 4px",
                                              color: "#666",
                                              wordBreak: "break-word",
                                            }}
                                          >
                                            {photo.FileName}
                                          </div>
                                        </div>
                                      )}

                                      {isImage && (
                                        <div
                                          style={{
                                            position: "absolute",
                                            bottom: "4px",
                                            left: "4px",
                                            right: "4px",
                                            background: "rgba(0,0,0,0.7)",
                                            color: "white",
                                            fontSize: "10px",
                                            padding: "2px 4px",
                                            borderRadius: "2px",
                                            textAlign: "center",
                                            pointerEvents: "none",
                                          }}
                                        >
                                          {photo.FileName}
                                        </div>
                                      )}

                                      {/* Иконка скачивания */}
                                      <div
                                        style={{
                                          position: "absolute",
                                          top: "4px",
                                          right: "4px",
                                          background: "rgba(0,0,0,0.7)",
                                          color: "white",
                                          borderRadius: "50%",
                                          width: "24px",
                                          height: "24px",
                                          display: "flex",
                                          alignItems: "center",
                                          justifyContent: "center",
                                          fontSize: "12px",
                                          pointerEvents: "none",
                                        }}
                                      >
                                        ⬇
                                      </div>
                                    </div>
                                  );
                                }
                              )}
                            </div>
                          </div>
                        )}

                      <FormRow>
                        <SmallFormGroup>
                          <Input
                            type="text"
                            value={
                              proposal.OfferPrice
                                ? proposal.OfferPrice.toString()
                                : "Не указано"
                            }
                            placeholder="Ваша цена"
                            disabled
                            style={{
                              backgroundColor: "#f8f9fa",
                              color: "#666",
                            }}
                          />
                          <span
                            style={{
                              position: "absolute",
                              right: "8px",
                              bottom: "8px",
                              color: "#656D78",
                              fontSize: "14px",
                            }}
                          >
                            ₸ за {material.PurchUnit}
                          </span>
                        </SmallFormGroup>

                        <ActionButtonContainer
                          style={{ border: "1px solid #f0f8ff" }}
                        >
                          <ActionButton
                            active={proposal.PriceWithDelivery === true}
                          >
                            Цена с доставкой
                          </ActionButton>
                          <ActionButton
                            active={proposal.PriceWithDelivery === false}
                          >
                            Без
                          </ActionButton>
                        </ActionButtonContainer>

                        <ActionButtonContainer
                          style={{ border: "1px solid #f0f8ff" }}
                        >
                          <ActionButton active={proposal.IsOriginal === true}>
                            Соответствует запросу
                          </ActionButton>
                          <ActionButton active={proposal.IsOriginal === false}>
                            Аналог
                          </ActionButton>
                        </ActionButtonContainer>
                      </FormRow>

                      <Label style={{ fontSize: "17px", color: "#656D78" }}>
                        Комментарий к предложению
                      </Label>

                      <TextArea
                        value={proposal.Description || "Комментарий не указан"}
                        placeholder="Комментарий к предложению"
                        disabled
                        style={{ backgroundColor: "#f8f9fa", color: "#666" }}
                      />
                      <DateButtonViewTenderButtonContainer>
                        <DateButton
                          disabled
                          style={{
                            backgroundColor: "#f8f9fa",
                            color: "#666",
                            borderColor: "#ddd",
                          }}
                        >
                          Ценовое предложение актуально до:{" "}
                          {proposal.ActualDate
                            ? new Date(proposal.ActualDate).toLocaleDateString(
                                "ru-RU"
                              )
                            : "Не указано"}
                          <img src="/icons/Cell/Vector.svg" alt="Календарь" />
                        </DateButton>
                        <ViewTenderButton
                          onClick={() =>
                            sendAcceptanceEmail(proposal, material)
                          }
                          disabled={sendingEmail}
                          style={{
                            opacity: sendingEmail ? 0.6 : 1,
                            cursor: sendingEmail ? "not-allowed" : "pointer",
                          }}
                        >
                          {sendingEmail ? "Отправка..." : "Принять предложение"}
                          <img src="/icons/findtender.svg" />
                        </ViewTenderButton>
                      </DateButtonViewTenderButtonContainer>
                    </ProductFormCard>
                  ))
                ) : (
                  <ProductFormCard style={{ marginBottom: "16px" }}>
                    <div
                      style={{
                        textAlign: "center",
                        padding: "20px",
                        color: "#666",
                        fontStyle: "italic",
                      }}
                    >
                      Ценовых предложений для этого материала пока нет
                    </div>
                  </ProductFormCard>
                )}
              </div>
            );
          })}
        </ContentContainer>
      </TenderProposalsContainer>

      {/* Модальное окно галереи изображений */}
      {isGalleryOpen && galleryImages.length > 0 && (
        <GalleryModal onClick={closeGallery}>
          <GalleryContainer onClick={(e) => e.stopPropagation()}>
            <GalleryClose onClick={closeGallery}>×</GalleryClose>

            {galleryImages.length > 1 && (
              <>
                <GalleryNav
                  direction="prev"
                  onClick={prevImage}
                  disabled={galleryImages.length <= 1}
                >
                  ‹
                </GalleryNav>
                <GalleryNav
                  direction="next"
                  onClick={nextImage}
                  disabled={galleryImages.length <= 1}
                >
                  ›
                </GalleryNav>
              </>
            )}

            <GalleryImage
              src={galleryImages[currentImageIndex]?.src}
              alt={galleryImages[currentImageIndex]?.alt}
              title={galleryImages[currentImageIndex]?.title}
            />

            <GalleryImageName>
              {galleryImages[currentImageIndex]?.title}
            </GalleryImageName>

            {galleryImages.length > 1 && (
              <GalleryCounter>
                {currentImageIndex + 1} / {galleryImages.length}
              </GalleryCounter>
            )}
          </GalleryContainer>
        </GalleryModal>
      )}

      {/* Старое модальное окно для совместимости */}
      {selectedImage && (
        <GalleryModal onClick={() => setSelectedImage(null)}>
          <GalleryContainer onClick={(e) => e.stopPropagation()}>
            <GalleryClose onClick={() => setSelectedImage(null)}>
              ×
            </GalleryClose>
            <GalleryImage
              src={selectedImage.src}
              alt={selectedImage.alt}
              title={selectedImage.title}
            />
            <GalleryImageName>{selectedImage.title}</GalleryImageName>
          </GalleryContainer>
        </GalleryModal>
      )}
    </>
  );
};

export default MyTenderProposalsClient;
