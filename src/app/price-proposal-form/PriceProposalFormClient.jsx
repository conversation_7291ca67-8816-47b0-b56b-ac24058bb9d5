"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import { useAuth } from "../../context/AuthContext";
import authService from "../../services/auth.service";
import API_CONFIG from "../../config/api";

const TenderFormContainer = styled.div`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background-color: white;
  min-height: 100vh;
  padding: 24px 20px;
`;
TenderFormContainer.displayName = "TenderFormContainer";

const ContentContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;
ContentContainer.displayName = "ContentContainer";

const Header = styled.div`
  border-bottom: 1px solid #dfe4e5;
  /* margin-bottom: 24px; */
`;
Header.displayName = "Header";

const HeaderContent = styled.div`
  margin: 0 auto;
  max-width: 1150px;
  display: flex;
  padding: 22px 40px 16px;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;

  @media (max-width: 768px) {
    padding: 16px 20px;
  }
`;
HeaderContent.displayName = "HeaderContent";

const BackButton = styled.button`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background-color: white;
  color: #434a54;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  gap: 8px;
  padding: 8px 20px;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f8f9fa;
  }
`;
BackButton.displayName = "BackButton";

const ClearAllButton = styled.button`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background-color: white;
  color: #434a54;
  border: 1px solid #d6dce1;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }
`;
ClearAllButton.displayName = "ClearAllButton";

const CreateTenderButton = styled.button`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background-color: #0066cc;
  color: white;
  border: none;
  padding: 6px 14px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #0055b3;
  }
`;
CreateTenderButton.displayName = "CreateTenderButton";

const Title = styled.h1`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  font-size: 36px;
  font-weight: 700;
  line-height: 120%;
  letter-spacing: -0.5px;
  color: #434a54;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    font-size: 28px;
  }
`;
Title.displayName = "Title";

const SectionTitle = styled.h2`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  font-size: 20px;
  font-weight: 700;
  line-height: 130%;
  color: #434a54;
  margin-top: 42px;
  margin-bottom: 24px;

  &:first-of-type {
    margin-top: 0;
  }
`;
SectionTitle.displayName = "SectionTitle";

const Text = styled.p`
  font-size: 17px;
  font-weight: 400;
  color: #434a54;
  margin-bottom: 16px;
  line-height: 1.5;
`;
Text.displayName = "Text";

const ProductFormCard = styled.div`
  background: #f5f5f5;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
  position: relative;
`;
ProductFormCard.displayName = "ProductFormCard";

const ProductHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
`;
ProductHeader.displayName = "ProductHeader";

const ProductInfo = styled.div`
  flex: 1;
`;
ProductInfo.displayName = "ProductInfo";

const ProductId = styled.div`
  font-size: 17px;
  color: #969ea7;
  margin-bottom: 10px;
`;
ProductId.displayName = "ProductId";

const ProductTitle = styled.h3`
  font-size: 24px;
  font-weight: 400;
  color: #434a54;
  line-height: 32px;
  margin-bottom: 10px;
`;
ProductTitle.displayName = "ProductTitle";

const Label = styled.div`
  font-size: 14px;
  color: #969ea7;
  margin-bottom: 10px;

  @media (max-width: 768px) {
    font-size: 17px;
  }
`;
Label.displayName = "Label";

const Input = styled.input`
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  height: 36px;
  width: 100%;

  &:focus {
    border-color: #0066cc;
    outline: none;
  }
`;
Input.displayName = "Input";

const FormRow = styled.div`
  display: flex;
  gap: 24px;
  padding: 10px 0;
  align-items: flex-end;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
  }
`;
FormRow.displayName = "FormRow";

const SmallFormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
`;
SmallFormGroup.displayName = "SmallFormGroup";

const ClearAllButtonContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;
ClearAllButtonContainer.displayName = "ClearAllButtonContainer";

const RemoveProductButton = styled.button`
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  font-size: 20px;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
  }
`;
RemoveProductButton.displayName = "RemoveProductButton";

const UploadButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #434a54;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f8f9fa;
  }
`;
UploadButton.displayName = "UploadButton";

const UploadText = styled.span`
  font-size: 14px;
  color: #434a54;
`;
UploadText.displayName = "UploadText";

const HiddenFileInput = styled.input`
  display: none;
`;
HiddenFileInput.displayName = "HiddenFileInput";

const AttachedFilesList = styled.div`
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
`;
AttachedFilesList.displayName = "AttachedFilesList";

const AttachedFileItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
`;
AttachedFileItem.displayName = "AttachedFileItem";

const FileInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2px;
`;
FileInfo.displayName = "FileInfo";

const FileName = styled.span`
  font-size: 14px;
  color: #333;
  font-weight: 500;
`;
FileName.displayName = "FileName";

const FileSize = styled.span`
  font-size: 12px;
  color: #6c757d;
`;
FileSize.displayName = "FileSize";

const RemoveFileButton = styled.button`
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  font-size: 18px;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
  }
`;
RemoveFileButton.displayName = "RemoveFileButton";

const CityDropdown = styled.div`
  position: relative;
  width: 300px;
`;
CityDropdown.displayName = "CityDropdown";

const CityButton = styled.button`
  width: 100%;
  padding: 12px 16px;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  color: #434a54;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;

  &:hover {
    border-color: #0066cc;
  }

  span {
    transition: transform 0.3s ease;
  }
`;
CityButton.displayName = "CityButton";

const CityDropdownList = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
  margin-top: 4px;
`;
CityDropdownList.displayName = "CityDropdownList";

const CityOption = styled.div`
  padding: 12px 16px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #f8f9fa;
  }

  &:not(:last-child) {
    border-bottom: 1px solid #f1f3f4;
  }
`;
CityOption.displayName = "CityOption";

const CityCheckbox = styled.div`
  width: 16px;
  height: 16px;
  border: 2px solid ${(props) => (props.checked ? "#0066cc" : "#ddd")};
  border-radius: 3px;
  background-color: ${(props) => (props.checked ? "#0066cc" : "white")};
  position: relative;

  ${(props) =>
    props.checked &&
    `
    &::after {
      content: '✓';
      position: absolute;
      top: -2px;
      left: 1px;
      color: white;
      font-size: 12px;
      font-weight: bold;
    }
  `}
`;
CityCheckbox.displayName = "CityCheckbox";

const RegionSection = styled.div`
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
`;
RegionSection.displayName = "RegionSection";

const NoProductsMessage = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: #666;
  font-size: 18px;
`;
NoProductsMessage.displayName = "NoProductsMessage";

const SuccessNotification = styled.div`
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #28a745;
  color: white;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  display: flex;
  align-items: center;
  gap: 12px;
  max-width: 400px;
`;
SuccessNotification.displayName = "SuccessNotification";

const NotificationText = styled.div`
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
`;
NotificationText.displayName = "NotificationText";

const CloseNotificationButton = styled.button`
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 18px;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    opacity: 0.8;
  }
`;
CloseNotificationButton.displayName = "CloseNotificationButton";

const PriceProposalFormClient = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();

  // Состояния
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [formData, setFormData] = useState([]);
  const [averagePrices, setAveragePrices] = useState([]);
  const [selectedCityId, setSelectedCityId] = useState("02"); // По умолчанию Алматы
  const [city, setCity] = useState("Алматы");
  const [isCityDropdownOpen, setIsCityDropdownOpen] = useState(false);
  const [showSuccessNotification, setShowSuccessNotification] = useState(false);

  // Список городов
  const cities = [
    {
      RegionId: "01",
      RegionName: "Нур-Султан",
      LivingWage: 22702,
      Coefficient: 1.155,
    },
    {
      RegionId: "02",
      RegionName: "Алматы",
      LivingWage: 22283,
      Coefficient: 1.134,
    },
    {
      RegionId: "04",
      RegionName: "Актюбинская область",
      LivingWage: 18010,
      Coefficient: 0.917,
    },
    {
      RegionId: "05",
      RegionName: "Алматинская область",
      LivingWage: 20557,
      Coefficient: 1.046,
    },
    {
      RegionId: "06",
      RegionName: "Атырауская область",
      LivingWage: 20297,
      Coefficient: 1.033,
    },
    {
      RegionId: "07",
      RegionName: "Западно-Казахстанская область",
      LivingWage: 17947,
      Coefficient: 0.913,
    },
    {
      RegionId: "08",
      RegionName: "Жамбылская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "09",
      RegionName: "Карагандинская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "10",
      RegionName: "Костанайская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "11",
      RegionName: "Кызылординская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "12",
      RegionName: "Мангистауская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "13",
      RegionName: "Туркестанская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "14",
      RegionName: "Павлодарская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "15",
      RegionName: "Северо-Казахстанская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "16",
      RegionName: "Восточно-Казахстанская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "17",
      RegionName: "Акмолинская область",
      LivingWage: 18246,
      Coefficient: 0.929,
    },
    {
      RegionId: "3 ",
      RegionName: "Шымкент",
      LivingWage: 20283,
      Coefficient: 1,
    },
  ];

  // Получаем товары из localStorage
  useEffect(() => {
    if (typeof window !== "undefined") {
      try {
        const saved = localStorage.getItem("selectedPriceProposalProducts");
        const savedPrices = localStorage.getItem("proposalAveragePrices");

        if (saved) {
          const products = JSON.parse(saved);
          setSelectedProducts(products);

          // Загружаем средние цены если есть
          let averagePricesData = [];
          if (savedPrices) {
            try {
              averagePricesData = JSON.parse(savedPrices);
              setAveragePrices(averagePricesData);
            } catch (error) {
              console.error("Ошибка при загрузке средних цен:", error);
            }
          }

          // Инициализируем formData для каждого товара с автозаполнением цен
          const initialFormData = products.map((product) => {
            // Ищем средние цены для этого материала
            const avgPrice = averagePricesData.find(
              (price) => price.MaterialId === product.MaterialId
            );

            return {
              productId: product.MaterialId,
              productName: product.MaterialName,
              productUnit: product.UnitId,
              retailPrice: avgPrice ? avgPrice.AvgRetailPrice.toString() : "",
              wholesalePrice: avgPrice ? avgPrice.AvgTradePrice.toString() : "",
              wholesaleFromQty: "",
              files: [],
            };
          });
          setFormData(initialFormData);
        }
      } catch (error) {
        console.error("Ошибка при загрузке данных из localStorage:", error);
      }
    }
  }, []);

  // Функции для работы с формой
  const handleFormDataChange = (index, field, value) => {
    setFormData((prev) => {
      const newData = [...prev];
      newData[index] = { ...newData[index], [field]: value };
      return newData;
    });
  };

  const handleBack = () => {
    router.push("/products/page/1?source=price-proposal");
  };

  const handleClearAll = () => {
    localStorage.removeItem("selectedPriceProposalProducts");
    router.push("/products/page/1?source=price-proposal");
  };

  const toggleCityDropdown = () => {
    setIsCityDropdownOpen(!isCityDropdownOpen);
  };

  const handleCitySelect = (selectedCity) => {
    setCity(selectedCity.RegionName);
    setSelectedCityId(selectedCity.RegionId);
    setIsCityDropdownOpen(false);
  };

  // Получаем текущий выбранный город
  const getCurrentCity = () => {
    return cities.find((city) => city.RegionId === selectedCityId) || cities[1]; // По умолчанию Алматы
  };

  const handleRemoveProduct = (index) => {
    const newProducts = selectedProducts.filter((_, i) => i !== index);
    const newFormData = formData.filter((_, i) => i !== index);

    setSelectedProducts(newProducts);
    setFormData(newFormData);

    // Обновляем localStorage
    localStorage.setItem(
      "selectedPriceProposalProducts",
      JSON.stringify(newProducts)
    );
  };

  // Функции для работы с файлами
  const handleFileUpload = (index, files) => {
    if (files && files.length > 0) {
      const fileArray = Array.from(files);

      // Проверяем размер файлов (максимум 10MB на файл)
      const maxSize = 10 * 1024 * 1024; // 10MB
      const oversizedFiles = fileArray.filter((file) => file.size > maxSize);

      if (oversizedFiles.length > 0) {
        alert(
          `Файлы слишком большие. Максимальный размер: 10MB\n${oversizedFiles
            .map((f) => f.name)
            .join("\n")}`
        );
        return;
      }

      setFormData((prev) => {
        const newData = [...prev];
        newData[index] = {
          ...newData[index],
          files: [...(newData[index].files || []), ...fileArray],
        };
        return newData;
      });
    }
  };

  const handleRemoveFile = (productIndex, fileIndex) => {
    setFormData((prev) => {
      const newData = [...prev];
      newData[productIndex] = {
        ...newData[productIndex],
        files: newData[productIndex].files.filter((_, i) => i !== fileIndex),
      };
      return newData;
    });
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const handleSubmit = async () => {
    if (!user || !user.userId || !user.companyId) {
      alert("Не удалось определить пользователя или компанию");
      return;
    }
    try {
      for (let i = 0; i < formData.length; i++) {
        const product = formData[i];
        const productMeta = selectedProducts.find(
          (p) => p.MaterialId === product.productId
        );
        const now = new Date().toISOString();
        const advertBody = {
          AdvertId: 0,
          AdvertDate: now,
          PurchReqId: 0,
          PurchReqLineId: 0,
          CompanyId: user.companyId,
          UserId: user.userId,
          MaterialId: product.productId,
          Code: productMeta?.Code || "",
          MaterialName: product.productName,
          RetailPrice: Number(product.retailPrice) || 0,
          FromUnit: Number(product.wholesaleFromQty) || 0,
          TradePrice: Number(product.wholesalePrice) || 0,
          CurrencyId: "KZT",
          UnitId: product.productUnit || "",
          MaterialDescription: productMeta?.MaterialDescription || "",
          RegionId: selectedCityId,
          CreatedBy: user.email || "",
          CreationTime: now,
          ModifiedBy: user.email || "",
          ModifiedTime: now,
        };
        // 1. POST /api/Adverts
        const res = await fetch(`${API_CONFIG.BASE_URL}/api/Adverts`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(advertBody),
        });
        if (!res.ok) {
          throw new Error(`Ошибка создания предложения: ${res.status}`);
        }
        const advertResult = await res.json();
        const advertId = advertResult.AdvertId;
        // 2. POST /api/Adverts/Files?advertId=...
        if (advertId && product.files && product.files.length > 0) {
          for (const file of product.files) {
            const formDataFile = new FormData();
            formDataFile.append("", file, file.name);
            const fileRes = await fetch(
              `${API_CONFIG.BASE_URL}/api/Adverts/Files?advertId=${advertId}`,
              {
                method: "POST",
                body: formDataFile,
              }
            );
            if (!fileRes.ok) {
              throw new Error(`Ошибка загрузки файла: ${fileRes.status}`);
            }
          }
        }
      }
      setShowSuccessNotification(true);
      setTimeout(() => {
        setShowSuccessNotification(false);
        router.push("/products/page/1?source=price-proposal");
      }, 3000);
    } catch (error) {
      alert(error.message || "Ошибка при отправке предложения");
    }
  };

  const handleCloseSuccessNotification = () => {
    setShowSuccessNotification(false);
  };

  if (selectedProducts.length === 0) {
    return (
      <TenderFormContainer>
        <ContentContainer>
          <NoProductsMessage>
            Нет выбранных товаров для создания ценового предложения.
            <br />
            <button
              onClick={() =>
                router.push("/products/page/1?source=price-proposal")
              }
              style={{
                marginTop: "16px",
                padding: "8px 16px",
                backgroundColor: "#0066cc",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer",
              }}
            >
              Выбрать товары
            </button>
          </NoProductsMessage>
        </ContentContainer>
      </TenderFormContainer>
    );
  }

  return (
    <>
      {/* Уведомление об успешном создании */}
      {showSuccessNotification && (
        <SuccessNotification>
          <NotificationText>
            Ценовое предложение успешно создано!
          </NotificationText>
          <CloseNotificationButton onClick={handleCloseSuccessNotification}>
            ×
          </CloseNotificationButton>
        </SuccessNotification>
      )}

      <Header>
        <HeaderContent>
          <BackButton onClick={handleBack}>
            <img
              src="/icons/arrow_back_24px.svg"
              alt="Назад"
              style={{ width: "12px", height: "12px" }}
            />
            НАЗАД К МОИМ ТЕНДЕРАМ
          </BackButton>
        </HeaderContent>
      </Header>

      <TenderFormContainer>
        <ContentContainer>
          <Title>Подача ценового предложения</Title>

          <Text>
            Укажите цены на выбранные материалы и прикрепите необходимые файлы.
          </Text>

          {/* Выбор региона */}
          <RegionSection>
            <SectionTitle style={{ marginTop: 0, marginBottom: 16 }}>
              Регион
            </SectionTitle>
            <CityDropdown data-city-dropdown>
              <CityButton onClick={toggleCityDropdown} type="button">
                {getCurrentCity().RegionName}
                <span
                  style={{
                    transform: isCityDropdownOpen
                      ? "rotate(180deg)"
                      : "rotate(0deg)",
                    transition: "transform 0.3s ease",
                  }}
                >
                  ▼
                </span>
              </CityButton>
              {isCityDropdownOpen && (
                <CityDropdownList>
                  {cities.map((cityOption) => (
                    <CityOption
                      key={cityOption.RegionId}
                      onClick={() => handleCitySelect(cityOption)}
                    >
                      <span>{cityOption.RegionName}</span>
                      <CityCheckbox
                        checked={selectedCityId === cityOption.RegionId}
                      />
                    </CityOption>
                  ))}
                </CityDropdownList>
              )}
            </CityDropdown>
          </RegionSection>

          {/* Формы для каждого материала */}
          {formData.map((product, index) => (
            <ProductFormCard key={product.productId}>
              <ProductHeader>
                <ProductInfo>
                  <ProductId>{product.productId}</ProductId>
                  <Label>Единица измерения: {product.productUnit}</Label>
                  <ProductTitle>{product.productName}</ProductTitle>
                </ProductInfo>

                {/* <ClearAllButtonContainer>
                  <ClearAllButton
                    onClick={() => handleRemoveProduct(index)}
                    style={{ padding: "12px" }}
                  >
                    <img
                      src="/icons/BusketCreateTender.svg"
                      width={"16"}
                      height={"16"}
                      alt="Удалить"
                    />
                  </ClearAllButton>
                </ClearAllButtonContainer> */}

                {/* <ClearAllButtonContainer>
                <RemoveProductButton
                  onClick={() => handleRemoveProduct(index)}
                  title="Удалить товар"
                >
                  ×
                </RemoveProductButton>
              </ClearAllButtonContainer> */}
              </ProductHeader>

              {/* Поля для цен */}
              <FormRow>
                <SmallFormGroup>
                  <Label>Розничная цена (тг)</Label>
                  <Input
                    type="number"
                    value={product.retailPrice}
                    onChange={(e) =>
                      handleFormDataChange(index, "retailPrice", e.target.value)
                    }
                    placeholder="0"
                  />
                </SmallFormGroup>

                <SmallFormGroup>
                  <Label>Оптовая цена (тг)</Label>
                  <Input
                    type="number"
                    value={product.wholesalePrice}
                    onChange={(e) =>
                      handleFormDataChange(
                        index,
                        "wholesalePrice",
                        e.target.value
                      )
                    }
                    placeholder="0"
                  />
                </SmallFormGroup>

                <SmallFormGroup>
                  <Label>Оптом от количества</Label>
                  <Input
                    type="number"
                    value={product.wholesaleFromQty}
                    onChange={(e) =>
                      handleFormDataChange(
                        index,
                        "wholesaleFromQty",
                        e.target.value
                      )
                    }
                    placeholder="0"
                  />
                </SmallFormGroup>
              </FormRow>

              {/* Прикрепление файлов для этого материала */}
              <div style={{ marginTop: "16px" }}>
                <Label>Прикрепить файлы</Label>
                <UploadButton
                  onClick={() =>
                    document.getElementById(`file-input-${index}`).click()
                  }
                  type="button"
                >
                  <img src="/icons/Upload.svg" alt="Загрузить" />
                  <UploadText>Прикрепить файл</UploadText>
                </UploadButton>

                <HiddenFileInput
                  id={`file-input-${index}`}
                  type="file"
                  multiple
                  onChange={(e) => handleFileUpload(index, e.target.files)}
                />

                {product.files && product.files.length > 0 && (
                  <AttachedFilesList>
                    {product.files.map((file, fileIndex) => (
                      <AttachedFileItem key={fileIndex}>
                        <FileInfo>
                          <FileName>{file.name}</FileName>
                          <FileSize>{formatFileSize(file.size)}</FileSize>
                        </FileInfo>
                        <RemoveFileButton
                          onClick={() => handleRemoveFile(index, fileIndex)}
                          title="Удалить файл"
                        >
                          ×
                        </RemoveFileButton>
                      </AttachedFileItem>
                    ))}
                  </AttachedFilesList>
                )}
              </div>
            </ProductFormCard>
          ))}

          {/* Кнопка отправки */}
          <div style={{ textAlign: "center", marginTop: "32px" }}>
            <CreateTenderButton
              onClick={handleSubmit}
              style={{ padding: "16px 32px", fontSize: "16px" }}
            >
              ОТПРАВИТЬ ЦЕНОВОЕ ПРЕДЛОЖЕНИЕ
              <img
                src="/icons/CheckCreateTender.svg"
                width={"15"}
                height={"15"}
                alt="Отправить"
              />
            </CreateTenderButton>
          </div>
        </ContentContainer>
      </TenderFormContainer>
    </>
  );
};

export default PriceProposalFormClient;
