import { notFound } from "next/navigation";
import Layout from "../../../../../../components/Layout";
import PriceList from "../../../../../../components/PriceList";
import CategorySEOContent from "../../../../../../components/CategorySEOContent";
import ApiService from "../../../../../../services/api.service";
import { ISRUtils } from "../../../../../../config/isr";

// Получение информации о категории по коду
async function getCategoryInfo(categoryCode) {
  try {
    const catalogData = await ApiService.getCatalog();

    // Поиск категории по коду во всей иерархии
    const findCategoryByCode = (items, code) => {
      for (const item of items) {
        if (item.Code === code) {
          return {
            id: item.MaterialTreeId,
            name: item.MaterialTreeName,
            code: item.Code,
            level: "department", // можно определить уровень по ParId
          };
        }
      }
      return null;
    };

    const category = findCategoryByCode(catalogData, categoryCode);
    return (
      category || {
        id: categoryCode,
        name: `Категория ${categoryCode}`,
        code: categoryCode,
        level: "unknown",
      }
    );
  } catch (error) {
    console.error("Ошибка при получении информации о категории:", error);
    return {
      id: categoryCode,
      name: `Категория ${categoryCode}`,
      code: categoryCode,
      level: "unknown",
    };
  }
}

// УМНАЯ генерация метаданных для категорий с поиском
export async function generateMetadata({ params, searchParams }) {
  const page = parseInt(params.page) || 1;
  const categoryCode = params.category;
  const { name, city } = searchParams || {};

  // Получаем информацию о категории
  const categoryInfo = await getCategoryInfo(categoryCode);

  // Определяем тип рендеринга
  const isSearch = !!name;

  // Логируем тип рендеринга для мониторинга
  ISRUtils.logISROperation("category-metadata", {
    page,
    categoryCode,
    isSearch,
    searchQuery: name,
    city,
    renderType: isSearch ? "SSR (поиск в категории)" : "SSR (категория)",
  });

  // Генерируем SEO-оптимизированные мета-теги для каталогов
  const categoryName = categoryInfo.name.toLowerCase();

  // Формируем коммерческие мета-теги
  let title, description, keywords;

  if (name && city) {
    // Поиск + город + категория
    title = `Купить ${name} в ${city} - каталог ${categoryInfo.name} - лучшие цены`;
    description = `${name} в ${city} Каталог ${categoryInfo.name} Лучшие цены Проверенные поставщики Быстрая доставка по ${city} Гарантия качества`;
  } else if (name) {
    // Поиск + категория
    title = `Купить ${name} - каталог ${categoryInfo.name} в Казахстане`;
    description = `${name} Каталог ${categoryInfo.name} Лучшие цены в Казахстане Широкий выбор Проверенные поставщики Быстрая доставка`;
  } else if (city) {
    // Категория + город
    title = `Купить ${categoryName} в ${city} - каталог строительных материалов`;
    description = `${categoryInfo.name} в ${city} Широкий каталог Лучшие цены Проверенные поставщики Доставка по ${city} Качественные материалы`;
  } else {
    // Только категория
    title = `Купить ${categoryName} в Казахстане - каталог ${categoryInfo.name}`;
    description = `${categoryInfo.name} Широкий каталог в Казахстане Лучшие цены Проверенные поставщики Быстрая доставка Гарантия качества`;
  }

  if (page > 1) {
    title += ` - страница ${page}`;
    description += ` - страница ${page}`;
  }

  // Генерируем расширенные ключевые слова
  const generateCategoryKeywords = (categoryName, searchQuery, city) => {
    const baseKeywords = [
      categoryInfo.name,
      `купить ${categoryName}`,
      `${categoryName} цена`,
      `${categoryName} казахстан`,
      `каталог ${categoryName}`,
      `${categoryName} поставщики`,
      `${categoryName} оптом`,
      `${categoryName} доставка`,
    ];

    if (searchQuery) {
      baseKeywords.push(
        searchQuery,
        `купить ${searchQuery}`,
        `${searchQuery} цена`,
        `${searchQuery} поставщики`
      );
    }

    if (city) {
      baseKeywords.push(
        `${categoryName} ${city}`,
        `купить ${categoryName} ${city}`,
        `${categoryName} доставка ${city}`
      );
      if (searchQuery) {
        baseKeywords.push(`${searchQuery} ${city}`);
      }
    }

    // Добавляем специфичные ключевые слова по категориям
    if (categoryName.includes("бетон")) {
      baseKeywords.push(
        "товарный бетон",
        "бетонная смесь",
        "бетон марки",
        "бетон класса"
      );
    }
    if (categoryName.includes("щебень")) {
      baseKeywords.push(
        "щебень фракции",
        "гранитный щебень",
        "щебень для дорог",
        "щебень для бетона"
      );
    }
    if (categoryName.includes("асфальт")) {
      baseKeywords.push(
        "асфальтная крошка",
        "асфальтобетон",
        "дорожный асфальт",
        "асфальт для дорог"
      );
    }
    if (categoryName.includes("дорог")) {
      baseKeywords.push(
        "дорожные материалы",
        "материалы для дорожного строительства",
        "дорожное покрытие"
      );
    }

    return baseKeywords;
  };

  keywords = generateCategoryKeywords(categoryName, name, city);

  const canonical = `https://shop.sadi.kz/products/category/${categoryCode}/page/${page}`;

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: "website",
      url: canonical,
      siteName: "Строительный маркетплейс",
      locale: "ru_RU",
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
    },
    alternates: {
      canonical,
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

// Серверный компонент для получения данных
async function getCategoryProductsData(categoryCode, page, searchParams) {
  console.log(
    "SSR Category: Загружаем данные для категории",
    categoryCode,
    "страница",
    page,
    "параметры:",
    searchParams
  );

  try {
    const pageSize = 20;
    const offset = (page - 1) * pageSize;

    // Формируем параметры запроса
    const queryParams = {
      offset,
      limit: pageSize,
      code: categoryCode, // Основной фильтр по категории
    };

    // Добавляем дополнительные фильтры
    if (searchParams.name) {
      queryParams.name = searchParams.name;
    }

    if (searchParams.city) {
      queryParams.city = searchParams.city;
    }

    // Получаем данные с сервера
    const filters = { code: categoryCode };
    if (queryParams.name) filters.query = queryParams.name;

    const response = await ApiService.getProductsWithPagination(
      page,
      pageSize,
      filters
    );

    // Получаем информацию о категории
    const categoryInfo = await getCategoryInfo(categoryCode);

    // Правильная структура для SSR
    const result = {
      products: response.data || response.products || [],
      totalCount: response.pagination?.totalItems || response.totalCount || 0,
      currentPage: page,
      totalPages:
        response.pagination?.totalPages ||
        Math.ceil(
          (response.pagination?.totalItems || response.totalCount || 0) /
            pageSize
        ),
      category: categoryInfo,
      filters: {
        name: searchParams.name || null,
        city: searchParams.city || null,
      },
    };

    console.log("SSR Category: Результат загрузки данных:", {
      categoryCode,
      categoryName: categoryInfo.name,
      productsCount: result.products.length,
      totalCount: result.totalCount,
      totalPages: result.totalPages,
      filters: result.filters,
    });

    return result;
  } catch (error) {
    console.error("Ошибка при получении данных товаров категории:", error);
    return {
      products: [],
      totalCount: 0,
      currentPage: page,
      totalPages: 0,
      category: { name: "Неизвестная категория", code: categoryCode },
      filters: {},
    };
  }
}

// Основной компонент страницы с умной логикой
export default async function CategoryProductsPage({ params, searchParams }) {
  const page = parseInt(params.page);
  const categoryCode = params.category;
  const { name } = searchParams || {};

  // Валидация параметров
  if (!page || page < 1 || !categoryCode) {
    notFound();
  }

  // УМНАЯ ЛОГИКА: определяем тип рендеринга
  const isSearch = !!name;

  // Логируем для мониторинга invocations
  ISRUtils.logISROperation("category-page-render", {
    page,
    categoryCode,
    isSearch,
    searchQuery: name,
    renderType: isSearch ? "SSR (поиск в категории)" : "SSR (категория)",
    timestamp: new Date().toISOString(),
  });

  // Получаем данные на сервере (всегда SSR для категорий, но логируем для мониторинга)
  const data = await getCategoryProductsData(categoryCode, page, searchParams);

  // Если страница больше общего количества страниц, показываем 404
  if (page > data.totalPages && data.totalPages > 0) {
    notFound();
  }

  // Формируем структурированные данные для поисковых систем
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    name: `${data.category.name} - страница ${page}`,
    description: `Каталог товаров категории "${data.category.name}"`,
    url: `https://shop.sadi.kz/products/category/${categoryCode}/page/${page}`,
    breadcrumb: {
      "@type": "BreadcrumbList",
      itemListElement: [
        {
          "@type": "ListItem",
          position: 1,
          name: "Главная",
          item: "https://shop.sadi.kz",
        },
        {
          "@type": "ListItem",
          position: 2,
          name: "Товары",
          item: "https://shop.sadi.kz/products/page/1",
        },
        {
          "@type": "ListItem",
          position: 3,
          name: data.category.name,
          item: `https://shop.sadi.kz/products/category/${categoryCode}`,
        },
      ],
    },
    mainEntity: {
      "@type": "ItemList",
      numberOfItems: data.totalCount,
      itemListElement: data.products.map((product, index) => ({
        "@type": "Product",
        position: (page - 1) * 20 + index + 1,
        name: product.MaterialName,
        description: product.Description || "",
        category: data.category.name,
        offers: {
          "@type": "Offer",
          price: product.RetailPrice || 0,
          priceCurrency: "KZT",
          availability: "https://schema.org/InStock",
        },
      })),
    },
  };

  return (
    <Layout>
      {/* Структурированные данные для поисковых систем */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />

      {/* Хлебные крошки для SEO */}
      <nav
        aria-label="Хлебные крошки"
        style={{ padding: "16px 24px", fontSize: "14px" }}
      >
        <a href="/" style={{ color: "#666", textDecoration: "none" }}>
          Главная
        </a>
        <span style={{ margin: "0 8px", color: "#999" }}>→</span>
        <a
          href="/products/page/1"
          style={{ color: "#666", textDecoration: "none" }}
        >
          Товары
        </a>
        <span style={{ margin: "0 8px", color: "#999" }}>→</span>
        <span style={{ color: "#333" }}>{data.category.name}</span>
        {page > 1 && (
          <>
            <span style={{ margin: "0 8px", color: "#999" }}>→</span>
            <span style={{ color: "#333" }}>Страница {page}</span>
          </>
        )}
      </nav>

      {/* Основной контент */}
      <PriceList
        initialData={data}
        isServerRendered={true}
        categoryInfo={data.category}
      />

      {/* SEO-контент для каталога */}
      <CategorySEOContent categoryInfo={data.category} />
    </Layout>
  );
}
