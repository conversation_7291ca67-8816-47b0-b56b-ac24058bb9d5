"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import API_CONFIG from "../../config/api";
import { useAuth } from "../../context/AuthContext";
import Map2GIS from "../../components/Map2GIS";

const CompaniesRegistryContainer = styled.div`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  padding: 24px;
  flex-grow: 1;

  @media (max-width: 768px) {
    padding: 16px;
  }
`;
CompaniesRegistryContainer.displayName = "CompaniesRegistryContainer";

const Title = styled.h1`
  font-size: 42px;
  line-height: 150%;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  margin-left: 12px;

  @media (max-width: 768px) {
    font-size: 22px;
    margin-bottom: 12px;
    display: none; /* Скрываем заголовок на мобильных, так как он уже есть в шапке */
  }
`;
Title.displayName = "Title";

const Description = styled.p`
  font-size: 17px;
  max-width: 656px;
  color: #666;
  margin-bottom: 24px;
  line-height: 150%;

  a {
    color: #0066cc;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  @media (max-width: 768px) {
    font-size: 13px;
    margin-bottom: 16px;
  }
`;
Description.displayName = "Description";

const LoadingMessage = styled.div`
  text-align: center;
  padding: 40px;
  font-size: 18px;
  color: #666;
`;
LoadingMessage.displayName = "LoadingMessage";

const ErrorMessage = styled.div`
  text-align: center;
  padding: 40px;
  font-size: 18px;
  color: #dc3545;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  margin: 20px 0;
`;
ErrorMessage.displayName = "ErrorMessage";

const NoCompaniesMessage = styled.div`
  text-align: center;
  padding: 60px 20px;
  font-size: 18px;
  color: #666;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin: 20px 0;
`;
NoCompaniesMessage.displayName = "NoCompaniesMessage";

const CompaniesGrid = styled.div`
  display: grid;
  gap: 20px;
  margin-top: 20px;

  @media (max-width: 768px) {
    gap: 16px;
  }
`;
CompaniesGrid.displayName = "CompaniesGrid";

const CompanyCard = styled.div`
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  animation: fadeInUp 0.3s ease forwards;
  opacity: 0;
  transform: translateY(20px);
  width: 650px;

  @keyframes fadeInUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  @media (max-width: 768px) {
    width: 100%;
  }
`;
CompanyCard.displayName = "CompanyCard";

const CompanyTitle = styled.h3`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  font-size: 18px;
  font-weight: 700;
  line-height: 130%;
  color: #333;
  margin: 0 0 16px 0;
`;
CompanyTitle.displayName = "CompanyTitle";

const CompanyInfoRow = styled.div`
  display: flex;
  align-items: center;

  margin-bottom: 16px;
  font-size: 14px;
  color: #666;
  gap: 8px;
`;
CompanyInfoRow.displayName = "CompanyInfoRow";

// Стили для пагинации (как в FindWorkTenderClient.jsx)
const PaginationContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 32px;
  padding: 20px 0;
`;
PaginationContainer.displayName = "PaginationContainer";

const PaginationButton = styled.button`
  width: 40px;
  height: 40px;
  border: 1px solid #e0e0e0;
  background-color: ${(props) => (props.active ? "#1976d2" : "white")};
  color: ${(props) => (props.active ? "white" : "#333")};
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover:not(:disabled) {
    background-color: ${(props) => (props.active ? "#1565c0" : "#f5f5f5")};
  }

  &:disabled {
    background-color: #f5f5f5;
    color: #ccc;
    cursor: not-allowed;
    border-color: #e0e0e0;
  }
`;
PaginationButton.displayName = "PaginationButton";

const PaginationDots = styled.span`
  color: #666;
  font-size: 14px;
  padding: 0 4px;
`;
PaginationDots.displayName = "PaginationDots";

// Новые стили для фильтров как на изображении
const FiltersContainer = styled.div`
  border-radius: 8px;
  margin-bottom: 24px;
`;
FiltersContainer.displayName = "FiltersContainer";

const FiltersRow = styled.div`
  display: flex;
  gap: 16px;
  align-items: end;
  //flex-wrap: wrap;
  margin-bottom: 12px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
`;
FiltersRow.displayName = "FiltersRow";

const FilterGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 220px;
  //width: 220px;

  @media (max-width: 768px) {
    min-width: 100%;
    width: 100%;
  }
`;
FilterGroup.displayName = "FilterGroup";

const FilterLabel = styled.label`
  font-size: 12px;
  font-weight: 500;
  color: #666;
  margin-bottom: 4px;
`;
FilterLabel.displayName = "FilterLabel";

// Стили для кнопок лет на рынке (как на изображении - соединенные кнопки)
const YearsButtonsContainer = styled.div`
  display: flex;
  gap: 0;
  height: 36px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #0066cc;
  width: fit-content;
`;
YearsButtonsContainer.displayName = "YearsButtonsContainer";

const YearButtonStyled = styled.button`
  background-color: ${(props) => (props.active ? "#0066cc" : "#f8f9fa")};
  color: ${(props) => (props.active ? "white" : "#434a54")};
  border: none;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  white-space: nowrap;
  height: 36px;
  transition: all 0.2s ease;
  min-width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: ${(props) => (props.active ? "#0056b3" : "#e9ecef")};
  }
`;
YearButtonStyled.displayName = "YearButtonStyled";

// Стили для кнопок Да/Нет (как на изображении - соединенные кнопки)
const YesNoContainer = styled.div`
  display: flex;
  gap: 0;
  height: 36px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #0066cc;
  width: 220px;

  @media (max-width: 768px) {
    width: 100%;
  }
`;
YesNoContainer.displayName = "YesNoContainer";

const YesNoButton = styled.button`
  background-color: ${(props) => (props.active ? "#0066cc" : "#f8f9fa")};
  color: ${(props) => (props.active ? "white" : "#434a54")};
  border: none;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  white-space: nowrap;
  height: 36px;
  transition: all 0.2s ease;
  min-width: 110px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: ${(props) => (props.active ? "#0056b3" : "#e9ecef")};
  }
`;
YesNoButton.displayName = "YesNoButton";

// Обновленные стили для инпутов
const FilterInput = styled.input`
  height: 36px;
  padding: 0 12px;
  border: 1px solid #dcdcdc;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
  width: 220px;

  @media (max-width: 768px) {
    width: 100%;
  }

  &::placeholder {
    color: #999;
  }

  &:focus {
    outline: none;
    border-color: #007bff;
  }
`;
FilterInput.displayName = "FilterInput";

// Обновленные стили для селектов
const FilterSelect = styled.select`
  height: 36px;
  padding: 0 12px;
  border: 1px solid #dcdcdc;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
  cursor: pointer;
  width: 220px;

  @media (max-width: 768px) {
    width: 100%;
  }

  &:focus {
    outline: none;
    border-color: #007bff;
  }
`;
FilterSelect.displayName = "FilterSelect";

// Стили для кнопки поиска
const SearchButtonStyled = styled.button`
  height: 36px;
  padding: 0 24px;
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  width: 220px;

  @media (max-width: 768px) {
    width: 100%;
  }

  &:hover {
    background-color: #218838;
  }

  &:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
  }
`;
SearchButtonStyled.displayName = "SearchButtonStyled";

// Удален дублирующий FilterSelect

// --- Стили для инпутов и чекбоксов из FindTenderClient.jsx ---
// Универсальные стили для всех input, checkbox, кнопок, выпадающих списков
const unifiedHeight = "36px";
const unifiedFont = "14px";
const unifiedRadius = "4px";
const unifiedPadding = "0 12px";
const unifiedWidth = "220px";

// --- Переопределяем стили ---
const InputWrapper = styled.div`
  display: flex;
  width: ${unifiedWidth};
  border: 1px solid #dcdcdc;
  border-radius: ${unifiedRadius};
  overflow: hidden;
  height: ${unifiedHeight};
`;
const Input = styled.input`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  flex: 1;
  padding: ${unifiedPadding};
  border: none;
  font-size: ${unifiedFont};
  font-weight: 400;
  outline: none;
  height: ${unifiedHeight};
  &::placeholder {
    color: #b0b0b0;
  }
`;
const CheckboxLabel = styled.label`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  display: flex;
  align-items: center;
  font-size: ${unifiedFont};
  font-weight: 400;
  color: #5d6670;
  height: ${unifiedHeight};
  width: ${unifiedWidth};
  input[type="checkbox"] {
    margin-right: 10px;
    width: 20px;
    height: 20px;
    min-width: 20px;
    min-height: 20px;
    border-radius: 4px;
    border: 2px solid #dcdcdc;
  }
`;
const SearchButton = styled.button`
  width: ${unifiedWidth};
  background-color: #0064d9;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  font-size: ${unifiedFont};
  border-radius: ${unifiedRadius};
  padding: ${unifiedPadding};
  height: ${unifiedHeight};
`;
// --- END стили ---

// Удалены старые стили - заменены на новые выше

const ResultsInfo = styled.div`
  font-size: 16px;
  color: #666;
  margin-bottom: 20px;
`;
ResultsInfo.displayName = "ResultsInfo";

// Стили для уведомлений
const ErrorNotification = styled.div`
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: ${(props) => (props.isSuccess ? "#28a745" : "#dc3545")};
  color: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 16px;
  min-width: 300px;
  max-width: 500px;
  animation: slideDown 0.3s ease-out;

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateX(-50%) translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
  }

  @media (max-width: 768px) {
    left: 10px;
    right: 10px;
    transform: none;
    font-size: 14px;
    padding: 12px 20px;
    max-width: 95%;
  }
`;
ErrorNotification.displayName = "ErrorNotification";

const NotificationCloseButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  margin-left: auto;
  opacity: 0.8;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 1;
  }
`;
NotificationCloseButton.displayName = "NotificationCloseButton";

// Стили для сайдбара с детальной информацией о компании
const SidebarOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: ${(props) => (props.isOpen ? 1 : 0)};
  visibility: ${(props) => (props.isOpen ? "visible" : "hidden")};
  transition: opacity 0.3s ease, visibility 0.3s ease;
`;
SidebarOverlay.displayName = "SidebarOverlay";

const SidebarContainer = styled.div`
  position: fixed;
  top: 0;
  right: 0;
  width: 520px;
  height: 100vh;
  background: #f5f5f5;
  box-shadow: -4px 0 12px rgba(0, 0, 0, 0.15);
  transform: translateX(${(props) => (props.isOpen ? "0" : "100%")});
  transition: transform 0.3s ease;
  z-index: 1001;
  overflow-y: auto;

  @media (max-width: 768px) {
    width: 100%;
  }
`;
SidebarContainer.displayName = "SidebarContainer";

const SidebarHeader = styled.div`
  padding: 20px 24px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  gap: 12px;
`;
SidebarHeader.displayName = "SidebarHeader";

const SearchIcon = styled.div`
  width: 24px;
  height: 24px;
  background: #666;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  &:before {
    content: "🔍";
    font-size: 12px;
  }
`;
SearchIcon.displayName = "SearchIcon";

const SidebarTitle = styled.h1`
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
  flex: 1;
`;
SidebarTitle.displayName = "SidebarTitle";

const SidebarCloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #e9ecef;
  }
`;
SidebarCloseButton.displayName = "SidebarCloseButton";

const SidebarContent = styled.div`
  padding: 0;
`;
SidebarContent.displayName = "SidebarContent";

const CompanyPhotoSection = styled.div`
  background: white;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  border-bottom: 1px solid #e0e0e0;
`;
CompanyPhotoSection.displayName = "CompanyPhotoSection";

const CompanyPhoto = styled.div`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;
CompanyPhoto.displayName = "CompanyPhoto";

const CompanyBasicInfo = styled.div`
  flex: 1;
`;
CompanyBasicInfo.displayName = "CompanyBasicInfo";

const CompanyBin = styled.div`
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
`;
CompanyBin.displayName = "CompanyBin";

const CompanyType = styled.div`
  font-size: 14px;
  color: #666;
`;
CompanyType.displayName = "CompanyType";

const LocationSection = styled.div`
  background: white;
  margin-top: 8px;
  border-bottom: 1px solid #e0e0e0;
`;
LocationSection.displayName = "LocationSection";

const LocationHeader = styled.div`
  padding: 20px 24px 16px;
  font-size: 14px;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;
LocationHeader.displayName = "LocationHeader";

const LocationAddress = styled.div`
  padding: 0 24px 16px;
  font-size: 16px;
  color: #333;
  line-height: 1.4;
`;
LocationAddress.displayName = "LocationAddress";

const MapContainer = styled.div`
  height: 200px;
  background: #e0e0e0;
  margin: 0 24px 24px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
`;
MapContainer.displayName = "MapContainer";

const InfoSection = styled.div`
  background: white;
  margin-top: 8px;
  padding: 20px 24px;
`;
InfoSection.displayName = "InfoSection";

const InfoSectionTitle = styled.div`
  font-size: 14px;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 20px;
`;
InfoSectionTitle.displayName = "InfoSectionTitle";

const InfoRow = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
`;
InfoRow.displayName = "InfoRow";

const InfoIcon = styled.div`
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
`;
InfoIcon.displayName = "InfoIcon";

const InfoText = styled.div`
  font-size: 16px;
  color: #333;
  line-height: 1.4;
`;
InfoText.displayName = "InfoText";

const ButtonsSection = styled.div`
  background: white;
  padding: 24px;
  margin-top: 8px;
  display: flex;
  gap: 12px;
`;
ButtonsSection.displayName = "ButtonsSection";

const ActionButton = styled.button`
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &.primary {
    background: #1976d2;
    color: white;

    &:hover {
      background: #1565c0;
    }
  }

  &.secondary {
    background: #f5f5f5;
    color: #666;

    &:hover {
      background: #e9ecef;
    }
  }
`;
ActionButton.displayName = "ActionButton";

const LoadingSidebar = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  font-size: 16px;
  color: #666;
  background: white;
  margin: 24px;
  border-radius: 8px;
`;
LoadingSidebar.displayName = "LoadingSidebar";

// Стили для модального окна отзыва
const ReviewModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: ${(props) => (props.isOpen ? 1 : 0)};
  visibility: ${(props) => (props.isOpen ? "visible" : "hidden")};
  transition: opacity 0.3s ease, visibility 0.3s ease;
`;
ReviewModalOverlay.displayName = "ReviewModalOverlay";

const ReviewModalContainer = styled.div`
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  transform: ${(props) => (props.isOpen ? "scale(1)" : "scale(0.9)")};
  transition: transform 0.3s ease;

  @media (max-width: 768px) {
    width: 95%;
    max-height: 95vh;
  }
`;
ReviewModalContainer.displayName = "ReviewModalContainer";

const ReviewModalHeader = styled.div`
  padding: 24px 24px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: none;
`;
ReviewModalHeader.displayName = "ReviewModalHeader";

const ReviewModalTitle = styled.h2`
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.2;
`;
ReviewModalTitle.displayName = "ReviewModalTitle";

const ReviewModalCloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: #e9ecef;
  }
`;
ReviewModalCloseButton.displayName = "ReviewModalCloseButton";

const ReviewModalContent = styled.div`
  padding: 24px;
`;
ReviewModalContent.displayName = "ReviewModalContent";

const ReviewFormSection = styled.div`
  margin-bottom: 24px;
`;
ReviewFormSection.displayName = "ReviewFormSection";

const ReviewFormLabel = styled.label`
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
`;
ReviewFormLabel.displayName = "ReviewFormLabel";

const ReviewTextarea = styled.textarea`
  width: 100%;
  min-height: 120px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  box-sizing: border-box;

  &::placeholder {
    color: #999;
  }

  &:focus {
    outline: none;
    border-color: #007bff;
  }
`;
ReviewTextarea.displayName = "ReviewTextarea";

const StarRatingContainer = styled.div`
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
`;
StarRatingContainer.displayName = "StarRatingContainer";

const StarButton = styled.button`
  background: none;
  border: none;
  font-size: 32px;
  cursor: pointer;
  color: ${(props) => (props.filled ? "#ffa500" : "#e0e0e0")};
  transition: color 0.2s ease;
  padding: 0;

  &:hover {
    color: #ffa500;
  }
`;
StarButton.displayName = "StarButton";

const ReviewModalButtons = styled.div`
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
`;
ReviewModalButtons.displayName = "ReviewModalButtons";

const ReviewButton = styled.button`
  padding: 12px 24px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &.cancel {
    background: #f5f5f5;
    color: #666;

    &:hover {
      background: #e9ecef;
    }
  }

  &.submit {
    background: #1976d2;
    color: white;

    &:hover {
      background: #1565c0;
    }

    &:disabled {
      background: #ccc;
      cursor: not-allowed;
    }
  }
`;
ReviewButton.displayName = "ReviewButton";

// Стили для модального окна приглашения в тендер
const InviteModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: ${(props) => (props.isOpen ? 1 : 0)};
  visibility: ${(props) => (props.isOpen ? "visible" : "hidden")};
  transition: opacity 0.3s ease, visibility 0.3s ease;
`;
InviteModalOverlay.displayName = "InviteModalOverlay";

const InviteModalContainer = styled.div`
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  transform: ${(props) => (props.isOpen ? "scale(1)" : "scale(0.9)")};
  transition: transform 0.3s ease;

  @media (max-width: 768px) {
    width: 95%;
    max-height: 95vh;
  }
`;
InviteModalContainer.displayName = "InviteModalContainer";

const InviteModalHeader = styled.div`
  padding: 24px 24px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: none;
`;
InviteModalHeader.displayName = "InviteModalHeader";

const InviteModalTitle = styled.h2`
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.2;
`;
InviteModalTitle.displayName = "InviteModalTitle";

const InviteModalCloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: #e9ecef;
  }
`;
InviteModalCloseButton.displayName = "InviteModalCloseButton";

const InviteModalContent = styled.div`
  padding: 24px;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
`;
InviteModalContent.displayName = "InviteModalContent";

const TendersList = styled.div`
  margin-bottom: 24px;
`;
TendersList.displayName = "TendersList";

const TenderItem = styled.div`
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }
`;
TenderItem.displayName = "TenderItem";

const TenderCheckbox = styled.div`
  width: 24px;
  height: 24px;
  border: 2px solid ${(props) => (props.checked ? "#1976d2" : "#e0e0e0")};
  border-radius: 4px;
  background: ${(props) => (props.checked ? "#1976d2" : "white")};
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;

  &:after {
    content: "✓";
    color: white;
    font-size: 14px;
    font-weight: bold;
    display: ${(props) => (props.checked ? "block" : "none")};
  }
`;
TenderCheckbox.displayName = "TenderCheckbox";

const TenderInfo = styled.div`
  flex: 1;
`;
TenderInfo.displayName = "TenderInfo";

const TenderName = styled.div`
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
`;
TenderName.displayName = "TenderName";

const TenderDetails = styled.div`
  font-size: 14px;
  color: #666;
`;
TenderDetails.displayName = "TenderDetails";

const InviteModalButtons = styled.div`
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 24px;
  border-top: 1px solid #e0e0e0;
  background: white;
  flex-shrink: 0;
`;
InviteModalButtons.displayName = "InviteModalButtons";

const InviteButton = styled.button`
  padding: 12px 24px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &.cancel {
    background: #f5f5f5;
    color: #666;

    &:hover {
      background: #e9ecef;
    }
  }

  &.submit {
    background: #1976d2;
    color: white;

    &:hover {
      background: #1565c0;
    }

    &:disabled {
      background: #ccc;
      cursor: not-allowed;
    }
  }
`;
InviteButton.displayName = "InviteButton";

const LoadingTenders = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  font-size: 16px;
  color: #666;
`;
LoadingTenders.displayName = "LoadingTenders";

// Данные регионов
const regions = [
  {
    RegionId: "01",
    RegionName: "Нур-Султан",
    LivingWage: 22702,
    Coefficient: 1.155,
  },
  {
    RegionId: "02",
    RegionName: "Алматы",
    LivingWage: 22283,
    Coefficient: 1.134,
  },
  {
    RegionId: "04",
    RegionName: "Актюбинская область",
    LivingWage: 18010,
    Coefficient: 0.917,
  },
  {
    RegionId: "05",
    RegionName: "Алматинская область",
    LivingWage: 20557,
    Coefficient: 1.046,
  },
  {
    RegionId: "06",
    RegionName: "Атырауская область",
    LivingWage: 20297,
    Coefficient: 1.033,
  },
  {
    RegionId: "07",
    RegionName: "Западно-Казахстанская область",
    LivingWage: 17947,
    Coefficient: 0.913,
  },
  {
    RegionId: "08",
    RegionName: "Жамбылская область",
    LivingWage: null,
    Coefficient: null,
  },
  {
    RegionId: "09",
    RegionName: "Карагандинская область",
    LivingWage: null,
    Coefficient: null,
  },
  {
    RegionId: "10",
    RegionName: "Костанайская область",
    LivingWage: null,
    Coefficient: null,
  },
  {
    RegionId: "11",
    RegionName: "Кызылординская область",
    LivingWage: null,
    Coefficient: null,
  },
  {
    RegionId: "12",
    RegionName: "Мангистауская область",
    LivingWage: null,
    Coefficient: null,
  },
  {
    RegionId: "13",
    RegionName: "Туркестанская область",
    LivingWage: null,
    Coefficient: null,
  },
  {
    RegionId: "14",
    RegionName: "Павлодарская область",
    LivingWage: null,
    Coefficient: null,
  },
  {
    RegionId: "15",
    RegionName: "Северо-Казахстанская область",
    LivingWage: null,
    Coefficient: null,
  },
  {
    RegionId: "16",
    RegionName: "Восточно-Казахстанская область",
    LivingWage: null,
    Coefficient: null,
  },
  {
    RegionId: "17",
    RegionName: "Акмолинская область",
    LivingWage: 18246,
    Coefficient: 0.929,
  },
  { RegionId: "3 ", RegionName: "Шымкент", LivingWage: 20283, Coefficient: 1 },
];

// Данные сфер деятельности
const businessScopes = [
  { id: 1, name: "Строительная компания", type: "Company" },
  { id: 2, name: "Грузоперевозка", type: "Carrier" },
  { id: 3, name: "Поставщик строительных материалов", type: "Provider" },
  { id: 4, name: "Проектировщик", type: "Company" },
  { id: 5, name: "Сметчик", type: "Company" },
];

// Стили для кастомных дропдаунов (как в FindWorkTenderClient.jsx)
const CitySelector = styled.div`
  width: 220px;

  @media (max-width: 768px) {
    width: 100%;
  }
`;
CitySelector.displayName = "CitySelector";

const CityDropdown = styled.div`
  position: relative;
  width: 100%;
`;
CityDropdown.displayName = "CityDropdown";

const CityButton = styled.button`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  width: 220px;
  height: 36px;
  background: white;
  border: 1px solid #dcdcdc;
  border-radius: 4px;
  padding: 0 16px;
  font-size: 14px;
  font-weight: 400;
  color: #333;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: left;

  @media (max-width: 768px) {
    width: 100%;
  }

  &:hover {
    border-color: #bbb;
  }

  &:focus {
    outline: none;
    border-color: #0066cc;
  }

  &:after {
    content: "▼";
    font-size: 12px;
    color: #666;
    transition: transform 0.2s ease;
    transform: ${(props) => (props.isOpen ? "rotate(180deg)" : "rotate(0deg)")};
  }
`;
CityButton.displayName = "CityButton";

const CityDropdownList = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  min-width: 200px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
`;
CityDropdownList.displayName = "CityDropdownList";

const CityOption = styled.div`
  padding: 12px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;

  &:hover {
    background-color: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }
`;
CityOption.displayName = "CityOption";

const CityCheckbox = styled.div`
  width: 20px;
  height: 20px;
  border: 2px solid ${(props) => (props.checked ? "#0066cc" : "#e0e0e0")};
  border-radius: 4px;
  background: ${(props) => (props.checked ? "#0066cc" : "white")};
  display: flex;
  align-items: center;
  justify-content: center;

  &:after {
    content: "✓";
    color: white;
    font-size: 14px;
    font-weight: bold;
    display: ${(props) => (props.checked ? "block" : "none")};
  }
`;
CityCheckbox.displayName = "CityCheckbox";

const CompaniesRegistryClient = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const [companies, setCompanies] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isFilterLoading, setIsFilterLoading] = useState(false); // Загрузка при фильтрации
  const [error, setError] = useState(null);

  // Состояние для серверной пагинации
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0); // Общее количество записей с сервера
  const itemsPerPage = 10; // 10 компаний на страницу

  // Состояние для фильтров
  const [selectedRegion, setSelectedRegion] = useState("");
  const [selectedBusinessScope, setSelectedBusinessScope] = useState("");
  const [searchByInn, setSearchByInn] = useState("");
  const [searchByName, setSearchByName] = useState("");
  const [yearsInMarket, setYearsInMarket] = useState("");
  const [rating, setRating] = useState("");
  const [onlyWithReviews, setOnlyWithReviews] = useState(false);

  // Состояния для кастомных дропдаунов
  const [isCityDropdownOpen, setIsCityDropdownOpen] = useState(false);
  const [selectedCity, setSelectedCity] = useState("Весь Казахстан");
  const [isScopeDropdownOpen, setIsScopeDropdownOpen] = useState(false);
  const [selectedScope, setSelectedScope] = useState("Все категории");
  const [isRatingDropdownOpen, setIsRatingDropdownOpen] = useState(false);
  const [selectedRating, setSelectedRating] = useState("Все компании");

  // Состояния для уведомлений
  const [showErrorNotification, setShowErrorNotification] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState("");
  const [isSuccessNotification, setIsSuccessNotification] = useState(false);

  // Состояния для сайдбара с детальной информацией о компании
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [companyDetails, setCompanyDetails] = useState(null);
  const [isLoadingDetails, setIsLoadingDetails] = useState(false);

  // Состояния для модального окна отзыва
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  const [reviewText, setReviewText] = useState("");
  const [reviewRating, setReviewRating] = useState(0);
  const [isSubmittingReview, setIsSubmittingReview] = useState(false);

  // Состояния для модального окна приглашения в тендер
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [tenders, setTenders] = useState([]);
  const [isLoadingTenders, setIsLoadingTenders] = useState(false);
  const [selectedTenders, setSelectedTenders] = useState([]);
  const [isInviting, setIsInviting] = useState(false);

  // Удалено состояние для debounce - больше не нужно

  // Загрузка компаний при инициализации компонента
  useEffect(() => {
    // Загружаем компании БЕЗ фильтров по умолчанию
    fetchCompanies({
      regionId: "",
      businessScopeId: "",
      bin: "",
      companyName: "",
      page: 1,
    });
  }, []);

  // Удален useEffect для timeout - больше не нужен

  // Серверная пагинация - используем данные напрямую с сервера
  const totalPages = Math.ceil(totalCount / itemsPerPage);
  const currentCompanies = companies; // Сервер уже возвращает нужную страницу

  console.log("Пагинация:", {
    totalCount,
    itemsPerPage,
    totalPages,
    currentPage,
    companiesLength: companies.length,
    shouldShowPagination: totalCount > itemsPerPage && totalPages > 1,
  });

  const fetchCompanies = async (filters = {}, isFilterChange = false) => {
    try {
      // Устанавливаем соответствующий индикатор загрузки
      if (isFilterChange) {
        setIsFilterLoading(true);
      } else {
        setIsLoading(true);
      }
      setError(null);

      // Формируем query параметры
      const queryParams = new URLSearchParams();

      // Добавляем фильтры если они есть
      if (filters.regionId) {
        queryParams.append("regionId", filters.regionId);
      }
      if (filters.businessScopeId) {
        queryParams.append("businessScopeId", filters.businessScopeId);
      }
      if (filters.bin) {
        queryParams.append("bin", filters.bin);
      }
      if (filters.companyName) {
        queryParams.append("companyName", filters.companyName);
      }

      // Добавляем пагинацию
      const offset = ((filters.page || currentPage) - 1) * itemsPerPage;
      queryParams.append("offset", offset.toString());
      queryParams.append("limit", itemsPerPage.toString());

      const url = `${
        API_CONFIG.BASE_URL
      }/api/Companies?${queryParams.toString()}`;
      console.log("Запрос компаний:", url);

      const response = await fetch(url, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log("Получены компании:", data);

        // Предполагаем, что сервер возвращает объект с полями companies и totalCount
        // Если сервер возвращает просто массив, адаптируем под это
        if (Array.isArray(data)) {
          setCompanies(data);
          // Если данных нет на текущей странице, корректируем totalCount
          if (data.length === 0 && (filters.page || currentPage) > 1) {
            // Нет данных на этой странице - устанавливаем totalCount на основе предыдущей страницы
            const actualTotalCount =
              ((filters.page || currentPage) - 1) * itemsPerPage;
            setTotalCount(actualTotalCount);
            console.log(
              "Скорректирован totalCount (нет данных):",
              actualTotalCount
            );
          } else if (data.length > 0) {
            // Есть данные - предполагаем что есть еще страницы (бесконечная пагинация)
            const currentOffset =
              ((filters.page || currentPage) - 1) * itemsPerPage;
            const estimatedTotal =
              currentOffset +
              data.length +
              (data.length === itemsPerPage ? itemsPerPage : 0);
            setTotalCount(estimatedTotal);
            console.log("Установлен estimatedTotal:", estimatedTotal);
          } else {
            setTotalCount(0);
          }
        } else {
          setCompanies(data.companies || data.data || []);
          // Используем totalCount с сервера, если есть
          if (data.totalCount || data.total) {
            setTotalCount(data.totalCount || data.total);
            console.log(
              "Установлен totalCount с сервера:",
              data.totalCount || data.total
            );
          } else {
            // Аналогичная логика для объекта
            const companies = data.companies || data.data || [];
            if (companies.length === 0 && (filters.page || currentPage) > 1) {
              const actualTotalCount =
                ((filters.page || currentPage) - 1) * itemsPerPage;
              setTotalCount(actualTotalCount);
            } else if (companies.length > 0) {
              const currentOffset =
                ((filters.page || currentPage) - 1) * itemsPerPage;
              const estimatedTotal =
                currentOffset +
                companies.length +
                (companies.length === itemsPerPage ? itemsPerPage : 0);
              setTotalCount(estimatedTotal);
            } else {
              setTotalCount(0);
            }
          }
        }
      } else {
        throw new Error(`Ошибка загрузки компаний: ${response.status}`);
      }
    } catch (error) {
      console.error("Ошибка при загрузке компаний:", error);
      setError(error.message);
    } finally {
      setIsLoading(false);
      setIsFilterLoading(false);
    }
  };

  // Функция для получения названия региона
  const getRegionName = (regionId) => {
    const region = regions.find((r) => r.RegionId.trim() === regionId?.trim());
    return region ? region.RegionName : "Не указан";
  };

  // Функция для получения названия сферы деятельности
  const getBusinessScopeName = (businessScopeId) => {
    const scope = businessScopes.find((s) => s.id === businessScopeId);
    return scope ? scope.name : "Не указана";
  };

  // Функции для серверной пагинации
  const handlePageChange = (page) => {
    console.log("Переход на страницу:", page);
    setCurrentPage(page);
    // Загружаем данные для новой страницы с ПРИМЕНЕННЫМИ фильтрами
    fetchCompanies(
      {
        ...appliedFilters,
        page: page,
      },
      true
    );
    // Прокручиваем к началу результатов
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      handlePageChange(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      handlePageChange(currentPage + 1);
    }
  };

  // Функция для генерации номеров страниц для отображения (как в Pagination.jsx)
  const getPageNumbers = () => {
    const pageNumbers = [];
    const maxPagesToShow = 7; // Увеличиваем количество видимых кнопок для лучшей навигации

    if (totalPages <= maxPagesToShow) {
      // Если общее количество страниц меньше или равно максимальному количеству для отображения,
      // показываем все страницы
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // Иначе показываем страницы вокруг текущей
      let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
      let endPage = startPage + maxPagesToShow - 1;

      if (endPage > totalPages) {
        endPage = totalPages;
        startPage = Math.max(1, endPage - maxPagesToShow + 1);
      }

      for (let i = startPage; i <= endPage; i++) {
        pageNumbers.push(i);
      }

      // Добавляем многоточие в начало, если нужно
      if (startPage > 1) {
        pageNumbers.unshift("...");
        pageNumbers.unshift(1);
      }

      // Добавляем многоточие в конец, если нужно
      if (endPage < totalPages) {
        pageNumbers.push("...");
        pageNumbers.push(totalPages);
      }
    }

    console.log("getPageNumbers:", { currentPage, totalPages, pageNumbers });
    return pageNumbers;
  };

  // Массив городов/регионов
  const cities = [
    { RegionId: "", RegionName: "Весь Казахстан" },
    ...regions.map((r) => ({
      RegionId: r.RegionId.trim(),
      RegionName: r.RegionName,
    })),
  ];

  // Обработчики для кастомных дропдаунов
  const toggleCityDropdown = () => {
    setIsCityDropdownOpen(!isCityDropdownOpen);
  };

  const handleCitySelect = (cityObj) => {
    setSelectedCity(cityObj.RegionName);
    setSelectedRegion(cityObj.RegionId);
    setIsCityDropdownOpen(false);
    // НЕ вызываем applyFilters - только при нажатии "НАЧАТЬ ПОИСК"
  };

  const toggleScopeDropdown = () => setIsScopeDropdownOpen((v) => !v);

  const handleScopeSelect = (scope) => {
    setSelectedScope(scope.name);
    setSelectedBusinessScope(scope.id ? String(scope.id) : "");
    setIsScopeDropdownOpen(false);
    // НЕ вызываем applyFilters - только при нажатии "НАЧАТЬ ПОИСК"
  };

  const toggleRatingDropdown = () => setIsRatingDropdownOpen((v) => !v);

  const handleRatingSelect = (option) => {
    setSelectedRating(option.label);
    setRating(option.value);
    setIsRatingDropdownOpen(false);
    // Пока не реализовано на сервере, оставляем как есть
  };

  const ratingOptions = [
    { value: "", label: "Все компании" },
    { value: "5", label: "5 звезд" },
    { value: "4", label: "4+ звезд" },
    { value: "3", label: "3+ звезд" },
  ];

  // Состояние для примененных фильтров (те что отправляются на сервер)
  const [appliedFilters, setAppliedFilters] = useState({
    regionId: "",
    businessScopeId: "",
    bin: "",
    companyName: "",
  });

  // Функция для применения фильтров с серверным запросом
  const applyFilters = (newFilters = {}) => {
    const filters = {
      regionId:
        newFilters.regionId !== undefined
          ? newFilters.regionId
          : selectedRegion,
      businessScopeId:
        newFilters.businessScopeId !== undefined
          ? newFilters.businessScopeId
          : selectedBusinessScope,
      bin: newFilters.bin !== undefined ? newFilters.bin : searchByInn,
      companyName:
        newFilters.companyName !== undefined
          ? newFilters.companyName
          : searchByName,
      page: 1,
    };

    setAppliedFilters(filters);
    setCurrentPage(1);
    fetchCompanies(filters, true);
  };

  // Удалена debouncedSearch - фильтры применяются только при нажатии кнопки

  // Функции для обработки фильтров (только изменяют состояние, НЕ отправляют запросы)
  const handleRegionChange = (e) => {
    const newRegion = e.target.value;
    setSelectedRegion(newRegion);
    // НЕ вызываем applyFilters - только при нажатии "НАЧАТЬ ПОИСК"
  };

  const handleBusinessScopeChange = (e) => {
    const newScope = e.target.value;
    setSelectedBusinessScope(newScope);
    // НЕ вызываем applyFilters - только при нажатии "НАЧАТЬ ПОИСК"
  };

  const handleSearchByInn = (e) => {
    const newInn = e.target.value;
    setSearchByInn(newInn);
    // НЕ вызываем applyFilters - только при нажатии "НАЧАТЬ ПОИСК"
  };

  const handleSearchByName = (e) => {
    const newName = e.target.value;
    setSearchByName(newName);
    // НЕ вызываем applyFilters - только при нажатии "НАЧАТЬ ПОИСК"
  };

  const handleYearsChange = (years) => {
    setYearsInMarket(years === yearsInMarket ? "" : years);
    // Пока не реализовано на сервере, оставляем как есть
  };

  const handleRatingChange = (e) => {
    const newRating = e.target.value;
    setRating(newRating);
    // Пока не реализовано на сервере, оставляем как есть
    // applyFilters({ rating: newRating });
  };

  const handleReviewsToggle = (value) => {
    setOnlyWithReviews(value === onlyWithReviews ? false : value);
    // Пока не реализовано на сервере, оставляем как есть
  };

  const handleSearch = () => {
    // Применяем текущие значения фильтров
    applyFilters({
      regionId: selectedRegion,
      businessScopeId: selectedBusinessScope,
      bin: searchByInn,
      companyName: searchByName,
    });
  };

  const clearFilters = () => {
    // Сбрасываем состояния фильтров
    setSelectedRegion("");
    setSelectedBusinessScope("");
    setSearchByInn("");
    setSearchByName("");
    setYearsInMarket("");
    setRating("");
    setOnlyWithReviews(false);
    setSelectedCity("Весь Казахстан");
    setSelectedScope("Все категории");
    setSelectedRating("Все компании");

    // Сбрасываем примененные фильтры
    const emptyFilters = {
      regionId: "",
      businessScopeId: "",
      bin: "",
      companyName: "",
    };
    setAppliedFilters(emptyFilters);

    // Загружаем данные без фильтров
    setCurrentPage(1);
    fetchCompanies({ ...emptyFilters, page: 1 }, true);
  };

  // Функции для уведомлений
  const showError = (message) => {
    setNotificationMessage(message);
    setIsSuccessNotification(false);
    setShowErrorNotification(true);
    setTimeout(() => {
      setShowErrorNotification(false);
    }, 5000);
  };

  const showSuccess = (message) => {
    setNotificationMessage(message);
    setIsSuccessNotification(true);
    setShowErrorNotification(true);
    setTimeout(() => {
      setShowErrorNotification(false);
    }, 5000);
  };

  const handleCloseErrorNotification = () => {
    setShowErrorNotification(false);
  };

  // Функция проверки авторизации и данных компании
  const checkAuthAndCompany = () => {
    if (!isAuthenticated) {
      showError("Для выполнения этого действия необходимо авторизоваться");
      setTimeout(() => {
        router.push("/auth?from=companies");
      }, 2000);
      return false;
    }

    if (
      !user?.userId ||
      user?.userId === "00000000-0000-0000-0000-000000000000" ||
      !user?.companyId ||
      user?.companyId === "0000" ||
      user?.companyId === 0
    ) {
      showError(
        "Для выполнения этого действия необходимо заполнить данные компании"
      );
      setTimeout(() => {
        router.push("/auth?from=company");
      }, 2000);
      return false;
    }

    return true;
  };

  // Функции для работы с сайдбаром
  const handleCompanyClick = async (company) => {
    setSelectedCompany(company);
    setIsSidebarOpen(true);
    setIsLoadingDetails(true);

    try {
      // Запрашиваем детальную информацию о компании
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/Companies/${company.CompanyId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (response.ok) {
        const detailData = await response.json();
        setCompanyDetails(detailData);
      } else {
        // Если запрос не удался, используем данные из списка
        setCompanyDetails(company);
      }
    } catch (error) {
      console.error("Ошибка при загрузке деталей компании:", error);
      // Используем данные из списка как fallback
      setCompanyDetails(company);
    } finally {
      setIsLoadingDetails(false);
    }
  };

  const handleCloseSidebar = () => {
    setIsSidebarOpen(false);
    setTimeout(() => {
      setSelectedCompany(null);
      setCompanyDetails(null);
    }, 300); // Задержка для анимации
  };

  // Функции для работы с модальным окном отзыва
  const handleOpenReviewModal = () => {
    if (!checkAuthAndCompany()) {
      return;
    }
    setIsReviewModalOpen(true);
  };

  const handleCloseReviewModal = () => {
    setIsReviewModalOpen(false);
    setReviewText("");
    setReviewRating(0);
  };

  const handleStarClick = (rating) => {
    setReviewRating(rating);
  };

  const handleSubmitReview = async () => {
    if (!reviewText.trim() || reviewRating === 0) {
      showError("Пожалуйста, заполните отзыв и укажите рейтинг");
      return;
    }

    setIsSubmittingReview(true);

    try {
      // Здесь будет отправка отзыва на сервер
      // Пока просто имитируем отправку
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Закрываем модальное окно
      handleCloseReviewModal();

      // Показываем уведомление об успехе
      showSuccess(
        `Отзыв о компании "${selectedCompany?.CompanyName}" успешно отправлен`
      );
    } catch (error) {
      console.error("Ошибка при отправке отзыва:", error);
      showError("Ошибка при отправке отзыва");
    } finally {
      setIsSubmittingReview(false);
    }
  };

  // Функции для работы с модальным окном приглашения в тендер
  const handleOpenInviteModal = async () => {
    if (!checkAuthAndCompany()) {
      return;
    }

    setIsInviteModalOpen(true);
    setIsLoadingTenders(true);

    try {
      // Загружаем тендеры пользователя
      await fetchUserTenders();
    } catch (error) {
      console.error("Ошибка при загрузке тендеров:", error);
      showError("Ошибка при загрузке тендеров");
    } finally {
      setIsLoadingTenders(false);
    }
  };

  const handleCloseInviteModal = () => {
    setIsInviteModalOpen(false);
    setTenders([]);
    setSelectedTenders([]);
  };

  const fetchUserTenders = async () => {
    if (!user?.userId) {
      showError("Не удалось определить пользователя");
      return;
    }

    try {
      // Загружаем тендеры без спецификации
      const tendersWithoutSpec = await fetchTendersFromAPI(false);

      // Загружаем тендеры со спецификацией
      const tendersWithSpec = await fetchTendersFromAPI(true);

      // Объединяем результаты
      const allTenders = [
        ...tendersWithoutSpec.map((tender) => ({
          ...tender,
          isSpecification: false,
        })),
        ...tendersWithSpec.map((tender) => ({
          ...tender,
          isSpecification: true,
        })),
      ];

      setTenders(allTenders);
    } catch (error) {
      console.error("Ошибка при загрузке тендеров:", error);
      throw error;
    }
  };

  const fetchTendersFromAPI = async (isSpecification) => {
    const url = `${API_CONFIG.BASE_URL}/api/PurchReqTables/GetCusMyTenders?UserId=${user.userId}&purchStatus=1&isSpecification=${isSpecification}`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (response.ok) {
      const data = await response.json();
      return Array.isArray(data) ? data : [];
    } else {
      throw new Error(`Ошибка загрузки тендеров: ${response.status}`);
    }
  };

  const handleTenderSelect = (tender) => {
    const tenderKey = `${tender.PurchReqId}-${tender.isSpecification}`;
    const isSelected = selectedTenders.some(
      (t) => `${t.PurchReqId}-${t.isSpecification}` === tenderKey
    );

    if (isSelected) {
      // Убираем тендер из выбранных
      setSelectedTenders(
        selectedTenders.filter(
          (t) => `${t.PurchReqId}-${t.isSpecification}` !== tenderKey
        )
      );
    } else {
      // Добавляем тендер к выбранным
      setSelectedTenders([...selectedTenders, tender]);
    }
  };

  const getRegionNameById = (regionId) => {
    const region = regions.find(
      (r) => r.RegionId.trim() === regionId?.toString().trim()
    );
    return region ? region.RegionName : "Не указан";
  };

  const handleInviteToTender = async () => {
    if (selectedTenders.length === 0) {
      showError("Выберите хотя бы один тендер для приглашения");
      return;
    }

    if (!companyDetails?.Email) {
      showError("У компании не указан email");
      return;
    }

    setIsInviting(true);

    try {
      // Формируем HTML для письма со всеми выбранными тендерами
      const emailHTML = await generateInviteEmailHTML(selectedTenders);

      // Формируем заголовок письма
      const title =
        selectedTenders.length === 1
          ? `Приглашение в тендер: ${selectedTenders[0].PurchReqName}`
          : `Приглашение в тендеры (${selectedTenders.length} шт.)`;

      // Отправляем приглашение
      const response = await fetch(`${API_CONFIG.BASE_URL}/api/Mail/Send`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          Email: companyDetails.Email,
          Title: title,
          Body: emailHTML,
        }),
      });

      if (response.ok) {
        console.log("Приглашение отправлено успешно");
        handleCloseInviteModal();

        // Показываем уведомление об успешной отправке
        const successMessage =
          selectedTenders.length === 1
            ? `Приглашение в тендер "${selectedTenders[0].PurchReqName}" успешно отправлено на ${companyDetails.Email}`
            : `Приглашения в ${selectedTenders.length} тендеров успешно отправлены на ${companyDetails.Email}`;

        showSuccess(successMessage);
      } else {
        throw new Error(`Ошибка отправки приглашения: ${response.status}`);
      }
    } catch (error) {
      console.error("Ошибка при отправке приглашения:", error);
      showError("Ошибка при отправке приглашения");
    } finally {
      setIsInviting(false);
    }
  };

  const generateInviteEmailHTML = async (tenders) => {
    // Если передан массив тендеров, обрабатываем каждый
    const tendersArray = Array.isArray(tenders) ? tenders : [tenders];

    let html = `
      <div style="font-family: Arial, sans-serif; background: #ffffff; padding: 30px; text-align: center; color: #000;">
        <h2 style="color:#00a0df; margin-top:0;">SADI.KZ</h2>
        <p style="font-size:18px; margin: 10px 0 30px;">Вам пришло приглашение от <a href="https://sadi.kz" style="color:#00a0df; text-decoration: none;">sadi.kz</a></p>
    `;

    // Обрабатываем каждый тендер
    for (let i = 0; i < tendersArray.length; i++) {
      const tender = tendersArray[i];
      const tenderNumber = tendersArray.length > 1 ? ` ${i + 1}` : "";

      html += `
        <div style="text-align: left; margin: 20px 0; border: 1px solid #e0e0e0; padding: 20px; border-radius: 8px;">
          <h3>Информация о тендере${tenderNumber}:</h3>
          <p><strong>Номер документа:</strong> ${
            tender.DocNum || "Не указан"
          }</p>
          <p><strong>Наименование тендера:</strong> ${
            tender.PurchReqName || "Не указано"
          }</p>
          <p><strong>Регион:</strong> ${getRegionNameById(tender.RegionId)}</p>
          <p><strong>Адрес доставки:</strong> ${
            tender.DeliveryAddress || "Не указан"
          }</p>
          <p><strong>Описание:</strong> ${
            tender.Description || "Не указано"
          }</p>
      `;

      // Если это тендер без спецификации, добавляем материалы
      if (!tender.isSpecification) {
        try {
          const materialsResponse = await fetch(
            `${API_CONFIG.BASE_URL}/api/PurchReqLines?purchReqId=${tender.PurchReqId}`,
            {
              method: "GET",
              headers: {
                "Content-Type": "application/json",
              },
            }
          );

          if (materialsResponse.ok) {
            const materials = await materialsResponse.json();

            if (materials && materials.length > 0) {
              html += `
                <div style="text-align: left; margin: 20px 0;">
                  <h4>Материалы:</h4>
                  <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <thead>
                      <tr style="background-color: #f5f5f5;">
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Наименование</th>
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Количество</th>
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Цена</th>
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Ед. изм.</th>
                      </tr>
                    </thead>
                    <tbody>
              `;

              for (const material of materials) {
                html += `
                  <tr>
                    <td style="border: 1px solid #ddd; padding: 8px;">${
                      material.MaterialName || "Не указано"
                    }</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">${
                      material.PurchQty || "Не указано"
                    }</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">${
                      material.PurchOpenPrice || "Не указана"
                    }</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">${
                      material.PurchUnit || "Не указано"
                    }</td>
                  </tr>
                `;

                if (material.Description) {
                  html += `
                    <tr>
                      <td colspan="4" style="border: 1px solid #ddd; padding: 8px; font-style: italic; background-color: #f9f9f9;">
                        Описание: ${material.Description}
                      </td>
                    </tr>
                  `;
                }
              }

              html += `
                    </tbody>
                  </table>
                </div>
              `;

              // Добавляем прикрепленные файлы для каждого материала
              for (const material of materials) {
                html = await addAttachmentsToHTML(
                  html,
                  tender.PurchReqId,
                  material.PurchReqLineId
                );
              }
            }
          }
        } catch (error) {
          console.error("Ошибка при загрузке материалов:", error);
        }
      } else {
        // Для тендеров со спецификацией добавляем файлы без привязки к материалу
        html = await addAttachmentsToHTML(html, tender.PurchReqId, null);
      }

      // Добавляем кнопку для перехода к тендеру
      html += `
          <div style="margin: 20px 0;">
            <a href="https://shop.sadi.kz/tender-proposal/${tender.PurchReqId}" style="background-color: #00a0df; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
              Перейти к тендеру${tenderNumber}
            </a>
          </div>
        </div>
      `;
    }

    // Добавляем общие кнопки в конце письма
    html += `
        <div style="margin: 30px 0;">
          <a href="https://sadi.kz" style="background-color: #6c757d; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
            Главная страница
          </a>
        </div>
      </div>
    `;

    return html;
  };

  const addAttachmentsToHTML = async (html, purchReqId, purchReqLineId) => {
    try {
      const attachmentsUrl = purchReqLineId
        ? `${API_CONFIG.BASE_URL}/api/PurchReqTablePhotos?purchReqId=${purchReqId}&purchReqLineId=${purchReqLineId}`
        : `${API_CONFIG.BASE_URL}/api/PurchReqTablePhotos?purchReqId=${purchReqId}&purchReqLineId=null`;

      const attachmentsResponse = await fetch(attachmentsUrl, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (attachmentsResponse.ok) {
        const attachments = await attachmentsResponse.json();

        if (attachments && attachments.length > 0) {
          html += `
            <div style="text-align: left; margin: 20px 0;">
              <h4>Прикрепленные файлы:</h4>
              <ul>
          `;

          attachments.forEach((attachment) => {
            html += `<li><a href="${
              attachment.FileUrl || "#"
            }" style="color: #00a0df;">${
              attachment.FileName || "Файл"
            }</a></li>`;
          });

          html += `
              </ul>
            </div>
          `;
        }
      }
    } catch (error) {
      console.error("Ошибка при загрузке прикрепленных файлов:", error);
    }

    return html;
  };

  if (isLoading) {
    return (
      <CompaniesRegistryContainer>
        <Title>Реестр компаний</Title>
        <LoadingMessage>Загрузка реестра компаний...</LoadingMessage>
      </CompaniesRegistryContainer>
    );
  }

  if (error) {
    return (
      <CompaniesRegistryContainer>
        <Title>Реестр компаний</Title>
        <ErrorMessage>Ошибка загрузки компаний: {error}</ErrorMessage>
      </CompaniesRegistryContainer>
    );
  }

  return (
    <CompaniesRegistryContainer>
      <Title>Реестр компаний</Title>
      <Description>
        Тысячи поставщиков со всего Казахстана в едином реестре.
      </Description>

      {/* Фильтры точно как на изображении */}
      <FiltersContainer>
        {/* Первая строка фильтров */}
        <FiltersRow>
          <FilterGroup>
            <FilterLabel>Регион</FilterLabel>
            <CitySelector>
              <CityDropdown>
                <CityButton
                  onClick={toggleCityDropdown}
                  isOpen={isCityDropdownOpen}
                >
                  {selectedCity}
                </CityButton>
                {isCityDropdownOpen && (
                  <CityDropdownList>
                    {cities.map((city) => (
                      <CityOption
                        key={city.RegionId}
                        onClick={() => handleCitySelect(city)}
                      >
                        <span>{city.RegionName}</span>
                        <CityCheckbox
                          checked={selectedCity === city.RegionName}
                        />
                      </CityOption>
                    ))}
                  </CityDropdownList>
                )}
              </CityDropdown>
            </CitySelector>
          </FilterGroup>

          <FilterGroup>
            <FilterLabel>Сфера деятельности</FilterLabel>
            <CitySelector>
              <CityDropdown>
                <CityButton
                  onClick={toggleScopeDropdown}
                  isOpen={isScopeDropdownOpen}
                >
                  {selectedScope}
                </CityButton>
                {isScopeDropdownOpen && (
                  <CityDropdownList>
                    <CityOption
                      key="all"
                      onClick={() =>
                        handleScopeSelect({ id: "", name: "Все категории" })
                      }
                    >
                      <span>Все категории</span>
                      <CityCheckbox
                        checked={selectedScope === "Все категории"}
                      />
                    </CityOption>
                    {businessScopes.map((scope) => (
                      <CityOption
                        key={scope.id}
                        onClick={() => handleScopeSelect(scope)}
                      >
                        <span>{scope.name}</span>
                        <CityCheckbox checked={selectedScope === scope.name} />
                      </CityOption>
                    ))}
                  </CityDropdownList>
                )}
              </CityDropdown>
            </CitySelector>
          </FilterGroup>

          <FilterGroup>
            <FilterLabel>Лет на рынке</FilterLabel>
            <YearsButtonsContainer>
              {["1", "2", "3", "4", "5+"].map((year) => (
                <YearButtonStyled
                  key={year}
                  active={yearsInMarket === year}
                  onClick={() => handleYearsChange(year)}
                >
                  {year}
                </YearButtonStyled>
              ))}
            </YearsButtonsContainer>
          </FilterGroup>

          <FilterGroup>
            <FilterLabel>Поиск по ИИН/БИН</FilterLabel>
            <FilterInput
              type="text"
              placeholder="Начните набирать..."
              value={searchByInn}
              onChange={handleSearchByInn}
            />
          </FilterGroup>

          <FilterGroup>
            <FilterLabel>Поиск по Названию</FilterLabel>
            <FilterInput
              type="text"
              placeholder="Начните набирать..."
              value={searchByName}
              onChange={handleSearchByName}
            />
          </FilterGroup>
        </FiltersRow>

        {/* Вторая строка фильтров */}
        <FiltersRow>
          <FilterGroup>
            <FilterLabel>Только с отзывами</FilterLabel>
            <YesNoContainer>
              <YesNoButton
                active={onlyWithReviews === true}
                onClick={() => handleReviewsToggle(true)}
              >
                Да
              </YesNoButton>
              <YesNoButton
                active={onlyWithReviews === false}
                onClick={() => handleReviewsToggle(false)}
              >
                Нет
              </YesNoButton>
            </YesNoContainer>
          </FilterGroup>

          <FilterGroup>
            <FilterLabel>Рейтинг</FilterLabel>
            <CitySelector>
              <CityDropdown>
                <CityButton
                  onClick={toggleRatingDropdown}
                  isOpen={isRatingDropdownOpen}
                >
                  {selectedRating}
                </CityButton>
                {isRatingDropdownOpen && (
                  <CityDropdownList>
                    {ratingOptions.map((option) => (
                      <CityOption
                        key={option.value}
                        onClick={() => handleRatingSelect(option)}
                      >
                        <span>{option.label}</span>
                        <CityCheckbox
                          checked={selectedRating === option.label}
                        />
                      </CityOption>
                    ))}
                  </CityDropdownList>
                )}
              </CityDropdown>
            </CitySelector>
          </FilterGroup>

          <FilterGroup>
            <SearchButtonStyled
              onClick={handleSearch}
              disabled={isFilterLoading}
            >
              {isFilterLoading ? "ЗАГРУЗКА..." : "НАЧАТЬ ПОИСК"}
            </SearchButtonStyled>
          </FilterGroup>

          <FilterGroup>
            <SearchButtonStyled
              onClick={clearFilters}
              disabled={isFilterLoading}
              style={{ backgroundColor: "#6c757d" }}
            >
              ОЧИСТИТЬ
            </SearchButtonStyled>
          </FilterGroup>
        </FiltersRow>
      </FiltersContainer>

      {/* Информация о результатах */}
      {/*<ResultsInfo>*/}
      {/*  {isFilterLoading ? (*/}
      {/*    "Загрузка..."*/}
      {/*  ) : (*/}
      {/*    `Найдено: ${totalCount} компани${totalCount === 1 ? 'я' : totalCount < 5 && totalCount > 1 ? 'и' : 'й'} (страница ${currentPage} из ${totalPages})`*/}
      {/*  )}*/}
      {/*</ResultsInfo>*/}

      {/* Показываем загрузку при фильтрации */}
      {isFilterLoading ? (
        <LoadingMessage>Применение фильтров...</LoadingMessage>
      ) : currentCompanies.length === 0 ? (
        <NoCompaniesMessage>
          {totalCount === 0
            ? "Компании не найдены."
            : "По выбранным фильтрам компании не найдены."}
        </NoCompaniesMessage>
      ) : (
        <CompaniesGrid>
          {currentCompanies.map((company, index) => (
            <CompanyCard
              key={company.CompanyId}
              style={{
                animationDelay: `${index * 0.05}s`,
                cursor: "pointer",
              }}
              onClick={() => handleCompanyClick(company)}
            >
              <CompanyTitle>{company.CompanyName}</CompanyTitle>

              <CompanyInfoRow>
                <img
                  style={{ width: "24px", height: "24px" }}
                  src="/icons/Vector1.svg"
                  alt="Business Scope"
                />
                {getBusinessScopeName(company.BusinessScopeId)}
              </CompanyInfoRow>

              <CompanyInfoRow>
                <img
                  style={{ width: "24px", height: "24px" }}
                  src="/icons/Vector2.svg"
                  alt="Region"
                />
                {getRegionName(company.RegionId)}
              </CompanyInfoRow>

              <CompanyInfoRow>
                <img
                  style={{ width: "24px", height: "24px" }}
                  src="/icons/Vector3.svg"
                  alt="Description"
                />
                {company.Description || "Не указано"}
              </CompanyInfoRow>

              <CompanyInfoRow>
                <img
                  style={{ width: "24px", height: "24px" }}
                  src="/icons/Vector4.svg"
                  alt="Address"
                />
                {company.ActualAddress ||
                  company.LegalAddress ||
                  "Адрес не указан"}
              </CompanyInfoRow>
            </CompanyCard>
          ))}
        </CompaniesGrid>
      )}

      {/* Пагинация */}
      {!isLoading &&
        !error &&
        !isFilterLoading &&
        totalCount > itemsPerPage &&
        totalPages > 1 && (
          <PaginationContainer>
            {/* Кнопка "Назад" */}
            <PaginationButton
              onClick={handlePrevPage}
              disabled={currentPage === 1}
            >
              <svg
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M10 12L6 8L10 4"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </PaginationButton>

            {/* Номера страниц */}
            {getPageNumbers().map((page, index) => (
              <React.Fragment key={index}>
                {page === "..." ? (
                  <PaginationDots>...</PaginationDots>
                ) : (
                  <PaginationButton
                    active={page === currentPage}
                    onClick={() => handlePageChange(page)}
                  >
                    {page}
                  </PaginationButton>
                )}
              </React.Fragment>
            ))}

            {/* Кнопка "Вперед" */}
            <PaginationButton
              onClick={handleNextPage}
              disabled={currentPage === totalPages}
            >
              <svg
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M6 4L10 8L6 12"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </PaginationButton>
          </PaginationContainer>
        )}

      {/* Уведомления */}
      {showErrorNotification && (
        <ErrorNotification isSuccess={isSuccessNotification}>
          <span>{notificationMessage}</span>
          <NotificationCloseButton onClick={handleCloseErrorNotification}>
            ×
          </NotificationCloseButton>
        </ErrorNotification>
      )}

      {/* Сайдбар с детальной информацией о компании */}
      {isSidebarOpen && (
        <>
          <SidebarOverlay isOpen={isSidebarOpen} onClick={handleCloseSidebar} />
          <SidebarContainer isOpen={isSidebarOpen}>
            <SidebarHeader>
              {/*<SearchIcon />*/}
              <img src="/icons/Vector5.svg" alt="Search" />
              <SidebarTitle>
                {selectedCompany?.CompanyName || "Информация о компании"}
              </SidebarTitle>
              <SidebarCloseButton onClick={handleCloseSidebar}>
                ×
              </SidebarCloseButton>
            </SidebarHeader>

            <SidebarContent>
              {isLoadingDetails ? (
                <LoadingSidebar>Загрузка информации...</LoadingSidebar>
              ) : companyDetails ? (
                <>
                  {/* Секция с фото и основной информацией */}
                  <CompanyPhotoSection>
                    <CompanyPhoto>
                      {/*{companyDetails.logo ? (*/}
                      {/*  <img src={companyDetails.logo} alt="Логотип компании" />*/}
                      {/*) : (*/}
                      {/*  <div style={{ color: '#999', fontSize: '24px' }}>👤</div>*/}
                      {/*)}*/}
                    </CompanyPhoto>
                    <CompanyBasicInfo>
                      <CompanyBin>
                        БИН: {companyDetails.Bin || "Не указан"}
                      </CompanyBin>
                      <CompanyType>
                        {getBusinessScopeName(companyDetails.BusinessScopeId)}
                      </CompanyType>
                    </CompanyBasicInfo>
                  </CompanyPhotoSection>

                  {/* Секция с адресом и картой */}
                  <LocationSection>
                    <LocationHeader>Фактическое расположение</LocationHeader>
                    <LocationAddress>
                      {companyDetails.ActualAddress || "Не указано"}
                    </LocationAddress>
                    <MapContainer>
                      <Map2GIS companyData={companyDetails} />
                    </MapContainer>
                  </LocationSection>

                  {/* Основная информация */}
                  <InfoSection>
                    <InfoSectionTitle>Основная информация</InfoSectionTitle>

                    {(companyDetails.Phone1 || companyDetails.Phone2) && (
                      <InfoRow>
                        {/*<InfoIcon>📞</InfoIcon>*/}
                        <img src="/icons/Vector6.svg" alt="Phone" />
                        <InfoText>
                          {companyDetails.Phone1}
                          {companyDetails.Phone2 &&
                            companyDetails.Phone1 &&
                            " "}
                          {companyDetails.Phone2}
                        </InfoText>
                      </InfoRow>
                    )}

                    {companyDetails.Email && (
                      <InfoRow>
                        {/*<InfoIcon>✉️</InfoIcon>*/}
                        <img src="/icons/Vector7.svg" alt="Email" />
                        <InfoText>{companyDetails.Email}</InfoText>
                      </InfoRow>
                    )}

                    <InfoRow>
                      {/*<InfoIcon>📍</InfoIcon>*/}
                      <img src="/icons/Vector4.svg" alt="Address" />
                      <InfoText>
                        Юридический адрес:{" "}
                        {companyDetails.LegalAddress || "Не указан"}
                      </InfoText>
                    </InfoRow>
                  </InfoSection>

                  {/* Кнопки действий */}
                  <ButtonsSection>
                    <ActionButton
                      className="primary"
                      onClick={handleOpenInviteModal}
                    >
                      ПРИГЛАСИТЬ В ТЕНДЕР
                    </ActionButton>
                    <ActionButton className="secondary">
                      В МОИ ПОСТАВЩИКИ
                    </ActionButton>
                    <ActionButton
                      className="secondary"
                      onClick={handleOpenReviewModal}
                    >
                      ОСТАВИТЬ ОТЗЫВ
                    </ActionButton>
                  </ButtonsSection>
                </>
              ) : (
                <div
                  style={{
                    padding: "24px",
                    textAlign: "center",
                    color: "#666",
                  }}
                >
                  Информация недоступна
                </div>
              )}
            </SidebarContent>
          </SidebarContainer>
        </>
      )}

      {/* Модальное окно для отзыва */}
      {isReviewModalOpen && (
        <ReviewModalOverlay
          isOpen={isReviewModalOpen}
          onClick={handleCloseReviewModal}
        >
          <ReviewModalContainer
            isOpen={isReviewModalOpen}
            onClick={(e) => e.stopPropagation()}
          >
            <ReviewModalHeader>
              <ReviewModalTitle>
                Отзыв о компании{" "}
                {selectedCompany?.CompanyName || "Актобе Монтаж Автоматика"}
              </ReviewModalTitle>
              <ReviewModalCloseButton onClick={handleCloseReviewModal}>
                ×
              </ReviewModalCloseButton>
            </ReviewModalHeader>

            <ReviewModalContent>
              <ReviewFormSection>
                <ReviewFormLabel>Напишите отзыв о компании</ReviewFormLabel>
                <ReviewTextarea
                  value={reviewText}
                  onChange={(e) => setReviewText(e.target.value)}
                  placeholder="Могу рекомендовать работать с этой компанией"
                />
              </ReviewFormSection>

              <ReviewFormSection>
                <ReviewFormLabel>Укажите количество звезд</ReviewFormLabel>
                <StarRatingContainer>
                  {[1, 2, 3, 4, 5].map((star) => (
                    <StarButton
                      key={star}
                      filled={star <= reviewRating}
                      onClick={() => handleStarClick(star)}
                    >
                      ★
                    </StarButton>
                  ))}
                </StarRatingContainer>
              </ReviewFormSection>

              <ReviewModalButtons>
                <ReviewButton
                  className="cancel"
                  onClick={handleCloseReviewModal}
                  disabled={isSubmittingReview}
                >
                  ОТМЕНА
                </ReviewButton>
                <ReviewButton
                  className="submit"
                  onClick={handleSubmitReview}
                  disabled={
                    isSubmittingReview ||
                    !reviewText.trim() ||
                    reviewRating === 0
                  }
                >
                  {isSubmittingReview ? "ОТПРАВКА..." : "ОТПРАВИТЬ ОТЗЫВ"}
                </ReviewButton>
              </ReviewModalButtons>
            </ReviewModalContent>
          </ReviewModalContainer>
        </ReviewModalOverlay>
      )}

      {/* Модальное окно для приглашения в тендер */}
      {isInviteModalOpen && (
        <InviteModalOverlay
          isOpen={isInviteModalOpen}
          onClick={handleCloseInviteModal}
        >
          <InviteModalContainer
            isOpen={isInviteModalOpen}
            onClick={(e) => e.stopPropagation()}
          >
            <InviteModalHeader>
              <InviteModalTitle>Пригласить в тендер</InviteModalTitle>
              <InviteModalCloseButton onClick={handleCloseInviteModal}>
                ×
              </InviteModalCloseButton>
            </InviteModalHeader>

            <InviteModalContent>
              {isLoadingTenders ? (
                <LoadingTenders>Загрузка тендеров...</LoadingTenders>
              ) : tenders.length === 0 ? (
                <div
                  style={{
                    textAlign: "center",
                    padding: "40px",
                    color: "#666",
                  }}
                >
                  У вас нет активных тендеров
                </div>
              ) : (
                <TendersList>
                  {tenders.map((tender) => (
                    <TenderItem
                      key={`${tender.PurchReqId}-${tender.isSpecification}`}
                      onClick={() => handleTenderSelect(tender)}
                    >
                      <TenderCheckbox
                        checked={selectedTenders.some(
                          (t) =>
                            t.PurchReqId === tender.PurchReqId &&
                            t.isSpecification === tender.isSpecification
                        )}
                      />
                      <TenderInfo>
                        <TenderName>
                          {tender.PurchReqName || "Без названия"}
                        </TenderName>
                        <TenderDetails>
                          {tender.DocNum && `№ ${tender.DocNum} • `}
                          {getRegionNameById(tender.RegionId)}
                          {tender.isSpecification && " • Со спецификацией"}
                        </TenderDetails>
                      </TenderInfo>
                    </TenderItem>
                  ))}
                </TendersList>
              )}
            </InviteModalContent>

            <InviteModalButtons>
              <InviteButton
                className="cancel"
                onClick={handleCloseInviteModal}
                disabled={isInviting}
              >
                ОТМЕНА
              </InviteButton>
              <InviteButton
                className="submit"
                onClick={handleInviteToTender}
                disabled={isInviting || selectedTenders.length === 0}
              >
                {isInviting ? "ОТПРАВКА..." : "ПРИГЛАСИТЬ В ТЕНДЕР"}
              </InviteButton>
            </InviteModalButtons>
          </InviteModalContainer>
        </InviteModalOverlay>
      )}
    </CompaniesRegistryContainer>
  );
};

export default CompaniesRegistryClient;
