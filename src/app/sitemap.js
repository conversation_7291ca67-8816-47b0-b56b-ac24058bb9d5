import ISR_CONFIG, { ISRUtils } from "../config/isr";

// Базовый URL сайта
const BASE_URL = "https://shop.sadi.kz";

// Генерация основного sitemap.xml (только уникальные статические страницы)
// Каталог и товары находятся в отдельных sitemap для избежания дублирования
export default async function sitemap() {
  const urls = [];

  try {
    // Статические страницы с приоритетами из ISR конфигурации
    // УБРАЛИ /products/page/1 чтобы избежать дублирования с sitemap-pages.xml
    urls.push(
      {
        url: BASE_URL,
        lastModified: new Date(),
        changeFrequency: "daily",
        priority: ISR_CONFIG.BOT_OPTIMIZATION.PRIORITIES.HOME,
      },
      {
        url: `${BASE_URL}/create-tender`,
        lastModified: new Date(),
        changeFrequency: "weekly",
        priority: 0.8,
      },
      {
        url: `${BASE_URL}/find-tender`,
        lastModified: new Date(),
        changeFrequency: "weekly",
        priority: 0.8,
      },
      {
        url: `${BASE_URL}/tender-form`,
        lastModified: new Date(),
        changeFrequency: "weekly",
        priority: 0.7,
      },
      {
        url: `${BASE_URL}/about`,
        lastModified: new Date(),
        changeFrequency: "monthly",
        priority: 0.6,
      }
    );

    // ОПТИМИЗАЦИЯ: Динамические страницы разделены по отдельным sitemap файлам:
    // - sitemap-pages.xml: страницы каталога /products/page/1-500
    // - sitemap-categories.xml: страницы категорий
    // - sitemap-products.xml: отдельные товары
    // Это избегает дублирования и оптимизирует индексацию

    ISRUtils.logISROperation("main-sitemap", {
      staticPages: 5, // Убрали /products/page/1 для избежания дублирования
      catalogPages: 0, // Отключены для экономии - все в sitemap-pages.xml
      totalUrls: urls.length,
      note: "Каталог доступен через sitemap-pages.xml",
    });
  } catch (error) {
    console.error("Ошибка при генерации sitemap:", error);
  }

  return urls;
}
