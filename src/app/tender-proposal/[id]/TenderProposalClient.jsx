"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import DatePicker, { registerLocale } from "react-datepicker";
import { ru } from "date-fns/locale";
import "react-datepicker/dist/react-datepicker.css";
import API_CONFIG from "../../../config/api";
import { useAuth } from "../../../context/AuthContext";
import authService from "../../../services/auth.service";

// Регистрируем русскую локализацию
registerLocale("ru", ru);

const TenderProposalContainer = styled.div`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background-color: white;
  min-height: 100vh;
  padding: 24px 20px;
`;
TenderProposalContainer.displayName = "TenderProposalContainer";

const ContentContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;
ContentContainer.displayName = "ContentContainer";

const Header = styled.div`
  border-bottom: 1px solid #dfe4e5;
`;
Header.displayName = "Header";

const HeaderContent = styled.div`
  margin: 0 auto;
  max-width: 1150px;
  display: flex;
  padding: 22px 40px 16px;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;

  @media (max-width: 768px) {
    padding: 16px 20px;
  }
`;
HeaderContent.displayName = "HeaderContent";

const BackButton = styled.button`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background-color: white;
  color: #434a54;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  gap: 8px;
  padding: 8px 20px;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f8f9fa;
  }
`;
BackButton.displayName = "BackButton";

const Title = styled.h1`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  font-size: 36px;
  font-weight: 700;
  line-height: 120%;
  letter-spacing: -0.5px;
  color: #434a54;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    font-size: 28px;
  }
`;
Title.displayName = "Title";

const SectionTitle = styled.h2`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  font-size: 20px;
  font-weight: 700;
  line-height: 130%;
  color: #434a54;
  margin-top: 42px;
  margin-bottom: 24px;

  &:first-of-type {
    margin-top: 0;
  }
`;
SectionTitle.displayName = "SectionTitle";

const Text = styled.p`
  font-size: 17px;
  font-weight: 400;
  color: #434a54;
  margin-bottom: 16px;
  line-height: 1.5;
`;
Text.displayName = "Text";

const TenderInfoCard = styled.div`
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid #e9ecef;
`;
TenderInfoCard.displayName = "TenderInfoCard";

const TenderTitle = styled.h3`
  font-size: 20px;
  font-weight: 700;
  color: #434a54;
  margin-bottom: 12px;
`;
TenderTitle.displayName = "TenderTitle";

const TenderDetail = styled.div`
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;

  strong {
    color: #333;
  }
`;
TenderDetail.displayName = "TenderDetail";

const ProductFormCard = styled.div`
  background: white;
  border-radius: 12px;
  padding: 0;
  margin-bottom: 24px;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  overflow: hidden;
`;
ProductFormCard.displayName = "ProductFormCard";

// Секция с информацией о материале (верхняя часть)
const MaterialInfoSection = styled.div`
  background: #f8f9fa;
  padding: 24px;
  border-bottom: 1px solid #e9ecef;
`;
MaterialInfoSection.displayName = "MaterialInfoSection";

// Заголовок секции
const SectionHeader = styled.h4`
  font-size: 22px;
  font-weight: 700;
  color: #434a54;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 12px;
`;
SectionHeader.displayName = "SectionHeader";

// Секция с формой предложения (нижняя часть)
const ProposalFormSection = styled.div`
  background: white;
  padding: 28px;
`;
ProposalFormSection.displayName = "ProposalFormSection";

// Контейнер для информационных элементов
const InfoGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 10px;
  margin-bottom: 10px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
`;
InfoGrid.displayName = "InfoGrid";

// Информационный элемент
const InfoItem = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
`;
InfoItem.displayName = "InfoItem";

// Иконка для информационного элемента
const InfoIcon = styled.div`
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  flex-shrink: 0;

  svg {
    width: 20px;
    height: 20px;
  }
`;
InfoIcon.displayName = "InfoIcon";

// Текст информационного элемента
const InfoText = styled.div`
  font-size: 16px;
  color: #434a54;
  line-height: 1.4;

  strong {
    color: #434a54;
    font-weight: 600;
    font-size: 17px;
  }
`;
InfoText.displayName = "InfoText";

// SVG иконки
const QuantityIcon = () => (
  <svg viewBox="0 0 24 24" fill="currentColor">
    <path d="M3 3h18v2H3V3zm0 4h18v2H3V7zm0 4h18v2H3v-2zm0 4h18v2H3v-2zm0 4h18v2H3v-2z" />
  </svg>
);

const PriceIcon = () => (
  <svg viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1.41 16.09V20h-2.67v-1.93c-1.71-.36-3.16-1.46-3.27-3.4h1.96c.1 1.05.82 1.87 2.65 1.87 1.96 0 2.4-.98 2.4-1.59 0-.83-.44-1.61-2.67-2.14-2.48-.6-4.18-1.62-4.18-3.67 0-1.72 1.39-2.84 3.11-3.21V4h2.67v1.95c1.86.45 2.79 1.86 2.85 3.39H14.3c-.05-1.11-.64-1.87-2.22-1.87-1.5 0-2.4.68-2.4 1.64 0 .84.65 1.39 2.67 1.91s4.18 1.39 4.18 3.91c-.01 1.83-1.38 2.83-3.12 3.16z" />
  </svg>
);

const CheckIcon = () => (
  <svg viewBox="0 0 24 24" fill="currentColor">
    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
  </svg>
);

const DeliveryIcon = () => (
  <svg viewBox="0 0 24 24" fill="currentColor">
    <path d="M20 8h-3V4H3c-1.1 0-2 .9-2 2v11h2c0 1.66 1.34 3 3 3s3-1.34 3-3h6c0 1.66 1.34 3 3 3s3-1.34 3-3h2v-5l-3-4zM6 18.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm13.5-9l1.96 2.5H17V9.5h2.5zm-1.5 9c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5z" />
  </svg>
);

const AttachmentIcon = () => (
  <svg viewBox="0 0 24 24" fill="currentColor">
    <path d="M2 12.5C2 9.46 4.46 7 7.5 7H18c2.21 0 4 1.79 4 4s-1.79 4-4 4H9.5C8.12 15 7 13.88 7 12.5S8.12 10 9.5 10H17v2H9.41c-.55 0-.55 1 0 1H18c1.1 0 2-.9 2-2s-.9-2-2-2H7.5C5.57 9 4 10.57 4 12.5S5.57 16 7.5 16H17v2H7.5C4.46 18 2 15.54 2 12.5z" />
  </svg>
);

const BoxIcon = () => (
  <svg viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2l3 3h4v14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2V5h4l3-3zm0 3L9 8v11h6V8l-3-3z" />
  </svg>
);

const ProposalIcon = () => (
  <svg viewBox="0 0 24 24" fill="currentColor">
    <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z" />
  </svg>
);

const ProductInfo = styled.div`
  flex: 1;
`;
ProductInfo.displayName = "ProductInfo";

// Стили для фотографий материалов
const ProductHeader = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
`;
ProductHeader.displayName = "ProductHeader";

const MaterialImagesContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;
MaterialImagesContainer.displayName = "MaterialImagesContainer";

// Новый контейнер для одного изображения с overlay
const SingleImageContainer = styled.div`
  position: relative;
  width: 120px;
  height: 120px;
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #e0e0e0;
  transition: all 0.2s ease;

  &:hover {
    border-color: #007bff;
    transform: scale(1.02);
  }
`;
SingleImageContainer.displayName = "SingleImageContainer";

// Основное изображение
const MainImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;
MainImage.displayName = "MainImage";

// Overlay для множественных изображений
const ImageOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  font-weight: 700;
  backdrop-filter: blur(1px);
`;
ImageOverlay.displayName = "ImageOverlay";

const NoImagePlaceholder = styled.div`
  width: 120px;
  height: 120px;
  background-color: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #6c757d;
  text-align: center;
  flex-shrink: 0;
`;
NoImagePlaceholder.displayName = "NoImagePlaceholder";

const ProductTitleContainer = styled.div`
  flex: 1;
`;
ProductTitleContainer.displayName = "ProductTitleContainer";

// Стили для модального окна галереи
const GalleryModal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
`;
GalleryModal.displayName = "GalleryModal";

const GalleryContainer = styled.div`
  position: relative;
  max-width: 90%;
  max-height: 90%;
  display: flex;
  align-items: center;
  justify-content: center;
`;
GalleryContainer.displayName = "GalleryContainer";

const GalleryImage = styled.img`
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
  border-radius: 8px;
`;
GalleryImage.displayName = "GalleryImage";

const GalleryClose = styled.button`
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  font-size: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10001;

  &:hover {
    background: white;
  }
`;
GalleryClose.displayName = "GalleryClose";

const GalleryNav = styled.button`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  font-size: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10001;
  transition: all 0.2s ease;

  &:hover {
    background: white;
    transform: translateY(-50%) scale(1.1);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: translateY(-50%);
  }

  ${(props) => (props.direction === "prev" ? "left: 20px;" : "right: 20px;")}
`;
GalleryNav.displayName = "GalleryNav";

const GalleryCounter = styled.div`
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
`;
GalleryCounter.displayName = "GalleryCounter";

const GalleryImageName = styled.div`
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;
GalleryImageName.displayName = "GalleryImageName";

const ProductId = styled.div`
  font-size: 17px;
  color: #969ea7;
  margin-bottom: 10px;
`;
ProductId.displayName = "ProductId";

const ProductTitle = styled.h3`
  font-size: 26px;
  font-weight: 700;
  color: #434a54;
  line-height: 1.3;
  margin-bottom: 12px;
`;
ProductTitle.displayName = "ProductTitle";

const Label = styled.div`
  font-size: 16px;
  font-weight: 600;
  line-height: 1.4;
  color: #6c757d;
  margin-bottom: 12px;

  @media (max-width: 768px) {
    font-size: 18px;
  }
`;
Label.displayName = "Label";

const Input = styled.input`
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  height: 36px;
  padding-right: 40px;

  &:focus {
    border-color: #0066cc;
    outline: none;
  }
`;
Input.displayName = "Input";

const FormRow = styled.div`
  display: flex;
  gap: 24px;
  padding: 10px 0;
  margin-bottom: 16px;
  align-items: flex-end;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
`;
FormRow.displayName = "FormRow";

const SmallFormGroup = styled.div`
  display: flex;
  flex-direction: column;
  max-width: 200px;
  position: relative;
`;
SmallFormGroup.displayName = "SmallFormGroup";

const TextArea = styled.textarea`
  padding: 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  min-height: 100px;
  width: 100%;
  resize: vertical;
  color: #333;
  margin-bottom: 12px;
  line-height: 1.5;

  &:focus {
    outline: none;
    border-color: #0066cc;
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
  }
`;
TextArea.displayName = "TextArea";

const ActionContainer = styled.div`
  display: flex;
  gap: 24px;
  margin-bottom: 16px;
  align-items: flex-end;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
`;
ActionContainer.displayName = "ActionContainer";

const ActionButtonContainer = styled.div`
  display: flex;
  gap: 0;
  height: 36px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #0066cc;
  width: fit-content;
`;
ActionButtonContainer.displayName = "ActionButtonContainer";

const ActionButton = styled.button`
  background-color: ${(props) => (props.active ? "#0066cc" : "#f8f9fa")};
  color: ${(props) => (props.active ? "white" : "#434a54")};
  border: none;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  white-space: nowrap;
  height: 36px;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => (props.active ? "#0056b3" : "#e9ecef")};
  }
`;
ActionButton.displayName = "ActionButton";

const SubmitButton = styled.button`
  background-color: #28a745;
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 32px auto 0;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #218838;
  }
`;
SubmitButton.displayName = "SubmitButton";

const NoTenderMessage = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: #666;
  font-size: 16px;
`;
NoTenderMessage.displayName = "NoTenderMessage";

const DateButton = styled.button`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  background: white;
  border: 2px solid #0066cc;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  color: #0066cc;
  font-weight: 600;
  margin-top: 0px;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f0f8ff;
  }
`;
DateButton.displayName = "DateButton";

const UploadButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #434a54;
  margin-bottom: 10px;
  margin-top: 10px;

  &:hover {
    background-color: #f8f9fa;
  }
`;
UploadButton.displayName = "UploadButton";

const UploadText = styled.span`
  font-size: 14px;
  color: #434a54;
`;
UploadText.displayName = "UploadText";

const HiddenFileInput = styled.input`
  display: none;
`;
HiddenFileInput.displayName = "HiddenFileInput";

const AttachedFilesList = styled.div`
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
`;
AttachedFilesList.displayName = "AttachedFilesList";

const AttachedFileItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
`;
AttachedFileItem.displayName = "AttachedFileItem";

const FileInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2px;
`;
FileInfo.displayName = "FileInfo";

const FileName = styled.span`
  font-size: 14px;
  color: #333;
  font-weight: 500;
`;
FileName.displayName = "FileName";

const FileSize = styled.span`
  font-size: 12px;
  color: #666;
`;
FileSize.displayName = "FileSize";

const RemoveFileButton = styled.button`
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  padding: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f5c6cb;
  }
`;
RemoveFileButton.displayName = "RemoveFileButton";

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;
ModalOverlay.displayName = "ModalOverlay";

const ModalContent = styled.div`
  background: white;
  border-radius: 12px;
  padding: 32px;
  max-width: 610px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
`;
ModalContent.displayName = "ModalContent";

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
`;
ModalHeader.displayName = "ModalHeader";

const ModalTitle = styled.h3`
  font-size: 20px;
  font-weight: 700;
  color: #333;
  margin: 0;
`;
ModalTitle.displayName = "ModalTitle";

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 4px;

  &:hover {
    color: #333;
  }
`;
CloseButton.displayName = "CloseButton";

const CalendarContainer = styled.div`
  .react-datepicker {
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    font-family: inherit;
    background: white;
    padding: 32px;
  }

  .react-datepicker__header {
    background: white;
    border-bottom: none;
    padding: 0 0 20px 0;
  }

  .react-datepicker__current-month {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
  }

  .react-datepicker__navigation {
    top: 32px;
    width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &:hover {
      color: #e9ecef;
    }

    &--previous {
      left: 20px;
    }

    &--next {
      right: 20px;
    }
  }

  .react-datepicker__navigation-icon {
    &::before {
      border-color: #666;
      border-width: 2px 2px 0 0;
      width: 8px;
      height: 8px;
    }
  }

  .react-datepicker__day-names {
    display: flex;
    justify-content: center;
    gap: 32px;
    margin-bottom: 10px;
  }

  .react-datepicker__day-name {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 500;
    color: #666;
    margin: 0;
  }

  .react-datepicker__month {
  }

  .react-datepicker__week {
    display: flex;
    justify-content: center;
    gap: 32px;
    margin-bottom: 0;
  }

  .react-datepicker__day {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 14px;
    font-weight: 400;
    color: #333;
    margin: 0;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    background: transparent;

    &:hover {
      background: #f0f8ff;
      color: #0066cc;
    }

    &--selected {
      background: #0066cc !important;
      color: white !important;
      font-weight: 600;
    }

    &--today {
      background: #e3f2fd;
      color: #0066cc;
      font-weight: 600;
    }

    &--weekend {
      color: #dc3545;
    }

    &--outside-month {
      color: #ccc;
    }

    &--disabled {
      color: #ccc;
      cursor: not-allowed;

      &:hover {
        background: transparent;
        color: #ccc;
      }
    }
  }

  .react-datepicker__triangle {
    display: none;
  }

  /* 📱 Медиа-запросы для адаптива */
  @media (max-width: 768px) {
    .react-datepicker {
      padding: 16px;
    }

    .react-datepicker__day,
    .react-datepicker__day-name {
      width: 32px;
      height: 32px;
      font-size: 13px;
    }

    .react-datepicker__current-month {
      font-size: 16px;
    }

    .react-datepicker__navigation {
      top: 24px;
      width: 22px;
      height: 22px;
    }

    .react-datepicker__day-names,
    .react-datepicker__week {
      gap: 16px;
    }
  }

  @media (max-width: 480px) {
    .react-datepicker {
      padding: 12px;
    }

    .react-datepicker__day,
    .react-datepicker__day-name {
      width: 28px;
      height: 28px;
      font-size: 12px;
    }

    .react-datepicker__current-month {
      font-size: 14px;
    }

    .react-datepicker__navigation {
      top: 20px;
      width: 20px;
      height: 20px;
    }

    .react-datepicker__day-names,
    .react-datepicker__week {
      gap: 12px;
    }
  }
`;
CalendarContainer.displayName = "CalendarContainer";

// Стили для уведомления об успешной отправке предложения
const SuccessNotification = styled.div`
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #28a745;
  color: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 500;
  max-width: 90%;
  animation: slideDown 0.3s ease-out;

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateX(-50%) translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
  }

  @media (max-width: 768px) {
    font-size: 14px;
    padding: 12px 20px;
    max-width: 95%;
  }
`;
SuccessNotification.displayName = "SuccessNotification";

const SuccessNotificationText = styled.span`
  flex: 1;
`;
SuccessNotificationText.displayName = "SuccessNotificationText";

const SuccessNotificationClose = styled.button`
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 18px;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }
`;
SuccessNotificationClose.displayName = "SuccessNotificationClose";

const ErrorNotification = styled.div`
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #dc3545;
  color: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 16px;
  min-width: 300px;
  max-width: 500px;
  animation: slideDown 0.3s ease-out;

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateX(-50%) translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
  }

  @media (max-width: 768px) {
    left: 10px;
    right: 10px;
    transform: none;
    font-size: 14px;
    padding: 12px 20px;
    max-width: 95%;
  }
`;
ErrorNotification.displayName = "ErrorNotification";

const TenderProposalClient = ({ tenderId }) => {
  const router = useRouter();
  const { user } = useAuth();
  const [tenderInfo, setTenderInfo] = useState(null);
  const [tenderDetails, setTenderDetails] = useState(null); // Детальная информация о тендере
  const [materialPhotos, setMaterialPhotos] = useState({}); // Фотографии материалов по purchReqLineId
  const [materialImages, setMaterialImages] = useState({}); // Фотографии материалов по materialId
  const [selectedImage, setSelectedImage] = useState(null); // Для модального окна с полноразмерным изображением
  const [galleryImages, setGalleryImages] = useState([]); // Массив изображений для галереи
  const [currentImageIndex, setCurrentImageIndex] = useState(0); // Текущий индекс изображения в галерее
  const [isGalleryOpen, setIsGalleryOpen] = useState(false); // Состояние открытия галереи
  const [proposalData, setProposalData] = useState([]);
  const [isDateModalOpen, setIsDateModalOpen] = useState(false);
  const [validUntilDate, setValidUntilDate] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showSuccessNotification, setShowSuccessNotification] = useState(false);
  const [showErrorNotification, setShowErrorNotification] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState("");
  const [submittedProposals, setSubmittedProposals] = useState(new Set());

  // Функция для загрузки материалов тендера
  const fetchTenderMaterials = async (purchReqId) => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqLines?purchReqId=${purchReqId}`
      );

      if (!response.ok) {
        throw new Error(`Ошибка загрузки материалов: ${response.status}`);
      }

      const materials = await response.json();
      return Array.isArray(materials) ? materials : [];
    } catch (err) {
      console.error("Ошибка при загрузке материалов:", err);
      throw err;
    }
  };

  // Функция для загрузки детальной информации о тендере
  const fetchTenderDetails = async (purchReqId) => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqTables/${purchReqId}`
      );
      if (response.ok) {
        const data = await response.json();
        setTenderDetails(data); // Устанавливаем детальную информацию
        return data;
      } else {
        console.warn(`Не удалось загрузить детали тендера ${purchReqId}`);
        return null;
      }
    } catch (error) {
      console.error("Ошибка при загрузке деталей тендера:", error);
      return null;
    }
  };

  // Функция для загрузки фотографий материала тендера
  const fetchMaterialPhotos = async (purchReqId, purchReqLineId) => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqTablePhotos?purchReqId=${purchReqId}&purchReqLineId=${purchReqLineId}`
      );
      if (response.ok) {
        const data = await response.json();
        return Array.isArray(data) ? data : [];
      } else {
        console.warn(
          `Не удалось загрузить фотографии материала ${purchReqLineId}`
        );
        return [];
      }
    } catch (error) {
      console.error("Ошибка при загрузке фотографий материала:", error);
      return [];
    }
  };

  // Функция для загрузки фотографий материалов по materialIds
  const fetchMaterialImages = async (materialIds) => {
    try {
      if (!materialIds || materialIds.length === 0) {
        return {};
      }

      // Формируем query параметры для materialIds
      const queryParams = materialIds
        .map((id) => `materialIds=${id}`)
        .join("&");
      const url = `${API_CONFIG.BASE_URL}/api/Adverts/Files/ByMaterial?${queryParams}&fileType=.png.jpg.jpeg`;

      console.log("Загружаем фотографии материалов:", url);

      const response = await fetch(url);
      if (response.ok) {
        const data = await response.json();
        console.log("Получены фотографии материалов:", data);
        return data;
      } else {
        console.warn(
          `Не удалось загрузить фотографии материалов: ${response.status}`
        );
        return {};
      }
    } catch (error) {
      console.error("Ошибка при загрузке фотографий материалов:", error);
      return {};
    }
  };

  // Функции для работы с галереей
  const openGallery = (images, startIndex = 0) => {
    const galleryData = images.map((image) => ({
      src: `${API_CONFIG.BASE_URL}/api/Adverts/File?certificateId=${image.CertificateId}`,
      alt: image.FileName,
      title: image.FileName,
    }));
    setGalleryImages(galleryData);
    setCurrentImageIndex(startIndex);
    setIsGalleryOpen(true);
  };

  const closeGallery = () => {
    setIsGalleryOpen(false);
    setGalleryImages([]);
    setCurrentImageIndex(0);
  };

  const nextImage = () => {
    setCurrentImageIndex((prev) =>
      prev < galleryImages.length - 1 ? prev + 1 : 0
    );
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) =>
      prev > 0 ? prev - 1 : galleryImages.length - 1
    );
  };

  // Обработка клавиш для навигации по галерее
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (!isGalleryOpen) return;

      switch (e.key) {
        case "ArrowLeft":
          prevImage();
          break;
        case "ArrowRight":
          nextImage();
          break;
        case "Escape":
          closeGallery();
          break;
        default:
          break;
      }
    };

    document.addEventListener("keydown", handleKeyPress);
    return () => document.removeEventListener("keydown", handleKeyPress);
  }, [isGalleryOpen, galleryImages.length]);

  // Компонент для отображения миниатюр фотографий материала
  const MaterialImageThumbnails = ({ materialId }) => {
    const images = materialImages[materialId] || [];

    const handleImageClick = () => {
      if (images.length > 0) {
        openGallery(images, 0);
      }
    };

    if (images.length === 0) {
      return (
        <MaterialImagesContainer>
          <NoImagePlaceholder>Нет фото</NoImagePlaceholder>
        </MaterialImagesContainer>
      );
    }

    const firstImage = images[0];
    const remainingCount = images.length - 1;

    return (
      <MaterialImagesContainer>
        <SingleImageContainer onClick={handleImageClick}>
          <MainImage
            src={`${API_CONFIG.BASE_URL}/api/Adverts/File?certificateId=${firstImage.CertificateId}`}
            alt={firstImage.FileName}
            title={firstImage.FileName}
            onError={(e) => {
              e.target.style.display = "none";
            }}
          />
          {remainingCount > 0 && <ImageOverlay>+{remainingCount}</ImageOverlay>}
        </SingleImageContainer>
      </MaterialImagesContainer>
    );
  };

  // Функция для скачивания файла
  const handleDownloadFile = (fileUrl, fileName) => {
    try {
      // Создаем временную ссылку для скачивания
      const link = document.createElement("a");
      link.href = fileUrl;
      link.download = fileName || "file";
      link.target = "_blank";

      // Добавляем ссылку в DOM, кликаем и удаляем
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Ошибка при скачивании файла:", error);
      // Fallback - открываем файл в новой вкладке
      window.open(fileUrl, "_blank");
    }
  };

  // Функция для определения типа файла
  const getFileType = (fileName) => {
    if (!fileName) return "unknown";
    const extension = fileName.toLowerCase().split(".").pop();

    const imageTypes = ["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg"];
    const documentTypes = ["pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx"];
    const archiveTypes = ["zip", "rar", "7z", "tar", "gz"];

    if (imageTypes.includes(extension)) return "image";
    if (documentTypes.includes(extension)) return "document";
    if (archiveTypes.includes(extension)) return "archive";
    return "unknown";
  };

  // Функция для получения иконки файла
  const getFileIcon = (fileName) => {
    const extension = fileName ? fileName.toLowerCase().split(".").pop() : "";

    switch (extension) {
      case "pdf":
        return "📄";
      case "doc":
      case "docx":
        return "📝";
      case "xls":
      case "xlsx":
        return "📊";
      case "ppt":
      case "pptx":
        return "📋";
      case "zip":
      case "rar":
      case "7z":
        return "📦";
      case "txt":
        return "📃";
      default:
        return "📎";
    }
  };

  useEffect(() => {
    const loadTenderData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Сначала пытаемся получить базовую информацию из localStorage
        let tenderBasicInfo = null;
        if (typeof window !== "undefined") {
          try {
            const saved = localStorage.getItem("selectedTenderInfo");
            const tender = saved ? JSON.parse(saved) : null;

            if (tender && tender.PurchReqId.toString() === tenderId) {
              tenderBasicInfo = tender;
            }
          } catch (error) {
            console.warn("Ошибка при чтении localStorage:", error);
          }
        }

        // Загружаем данные параллельно
        const [materials, tenderDetailsData] = await Promise.all([
          fetchTenderMaterials(tenderId),
          fetchTenderDetails(tenderId),
        ]);

        // Сохраняем детальную информацию
        setTenderDetails(tenderDetailsData);

        // Формируем полную информацию о тендере
        const fullTenderInfo = {
          PurchReqId: tenderId,
          PurchReqName:
            tenderDetailsData?.PurchReqName ||
            tenderBasicInfo?.PurchReqName ||
            `Тендер №${tenderId}`,
          PurchEndDate:
            tenderDetailsData?.PurchEndDate ||
            tenderBasicInfo?.PurchEndDate ||
            null,
          DeliveryAddress:
            tenderDetailsData?.DeliveryAddress ||
            tenderBasicInfo?.DeliveryAddress ||
            null,
          Description:
            tenderDetailsData?.Description ||
            tenderBasicInfo?.Description ||
            null,
          materials: materials,
        };

        setTenderInfo(fullTenderInfo);

        // Инициализируем форму для каждого материала
        setProposalData(
          materials.map((material) => ({
            materialId: material.MaterialId,
            materialName: material.MaterialName,
            purchUnit: material.PurchUnit,
            purchQty: material.PurchQty,
            purchOpenPrice: material.PurchOpenPrice,
            Description: material.Description,
            purchReqLineId: material.PurchReqLineId,
            // Значения из API для отображения требований заказчика
            originalOnly: material.OriginalOnly, // true - соответствует запросу, false - можно аналог
            priceWithDelivery: material.PriceWithDelivery, // true - с доставкой, false - без
            // Значения для формы предложения (по умолчанию)
            retailPrice: "",
            priceType: "with-delivery", // "with-delivery" или "without-delivery"
            matchesRequest: true, // true - соответствует запросу, false - аналог
            comments: "",
            attachedFiles: [], // массив прикрепленных файлов
          }))
        );

        // Загружаем фотографии для каждого материала
        const photosPromises = materials.map(async (material) => {
          const photos = await fetchMaterialPhotos(
            tenderId,
            material.PurchReqLineId
          );
          return {
            purchReqLineId: material.PurchReqLineId,
            photos: photos,
          };
        });

        const photosResults = await Promise.all(photosPromises);
        const photosData = {};
        photosResults.forEach(({ purchReqLineId, photos }) => {
          photosData[purchReqLineId] = photos;
        });

        setMaterialPhotos(photosData);

        // Загружаем фотографии материалов по materialIds (новый способ)
        const materialIds = materials
          .map((material) => material.MaterialId)
          .filter((id) => id);
        if (materialIds.length > 0) {
          const materialImagesData = await fetchMaterialImages(materialIds);
          setMaterialImages(materialImagesData);
        }
      } catch (err) {
        setError(err.message);
        console.error("Ошибка при загрузке данных тендера:", err);
      } finally {
        setIsLoading(false);
      }
    };

    if (tenderId) {
      loadTenderData();
    }
  }, [tenderId]);

  const handleProposalDataChange = (index, field, value) => {
    setProposalData((prev) => {
      const newData = [...prev];
      newData[index] = { ...newData[index], [field]: value };
      return newData;
    });
  };

  const handleSubmit = () => {
    // Валидация
    const incompleteProposals = proposalData.filter(
      (proposal) => !proposal.retailPrice
    );

    if (incompleteProposals.length > 0) {
      alert("Укажите цены для всех материалов");
      return;
    }

    const submissionData = {
      tenderId: tenderInfo.PurchReqId,
      tenderName: tenderInfo.PurchReqName,
      validUntilDate: validUntilDate,
      proposals: proposalData.map((proposal) => ({
        ...proposal,
        attachedFilesCount: proposal.attachedFiles
          ? proposal.attachedFiles.length
          : 0,
        attachedFilesInfo: proposal.attachedFiles
          ? proposal.attachedFiles.map((f) => ({
              name: f.name,
              size: f.size,
              type: f.type,
            }))
          : [],
      })),
      submittedAt: new Date().toISOString(),
    };

    console.log("Подача предложения:", submissionData);

    // В реальном приложении здесь бы был FormData для отправки файлов
    const totalFiles = proposalData.reduce(
      (total, proposal) =>
        total + (proposal.attachedFiles ? proposal.attachedFiles.length : 0),
      0
    );

    if (totalFiles > 0) {
      console.log(`Прикреплено файлов: ${totalFiles}`);
    }
    alert("Предложение успешно отправлено!");

    // Очищаем localStorage и возвращаемся к поиску тендеров
    try {
      localStorage.removeItem("selectedTenderInfo");
    } catch (error) {
      console.error("Ошибка при очистке localStorage:", error);
    }

    router.push("/find-tender");
  };

  // Функция для загрузки файлов предложения
  const uploadProposalFiles = async (purchReqPriceId, attachedFiles) => {
    if (!attachedFiles || attachedFiles.length === 0) {
      console.log("📎 Нет файлов для загрузки");
      return;
    }

    try {
      const uploadPromises = attachedFiles.map(async (fileObj, index) => {
        const formData = new FormData();
        formData.append("file", fileObj.file, fileObj.name);

        console.log(
          `📤 Загружаем файл ${index + 1}/${attachedFiles.length}: ${
            fileObj.name
          }`
        );

        const response = await fetch(
          `${API_CONFIG.BASE_URL}/api/PurchReqPricePhotos?purchReqPriceId=${purchReqPriceId}`,
          {
            method: "POST",
            body: formData,
          }
        );

        if (!response.ok) {
          throw new Error(
            `Ошибка загрузки файла ${fileObj.name}: ${response.status}`
          );
        }

        const result = await response.json();
        console.log(`✅ Файл ${fileObj.name} успешно загружен:`, result);
        return result;
      });

      const results = await Promise.all(uploadPromises);
      console.log("✅ Все файлы успешно загружены:", results);
      return results;
    } catch (error) {
      console.error("❌ Ошибка при загрузке файлов:", error);
      // Не прерываем процесс, просто логируем ошибку
      alert(
        `Предложение отправлено, но возникла ошибка при загрузке файлов: ${error.message}`
      );
      return null;
    }
  };

  const handleSubmitSingleProposal = async (index) => {
    const proposal = proposalData[index];

    // Валидация для конкретного материала
    if (!proposal.retailPrice) {
      alert("Укажите цену для материала");
      return;
    }

    if (!validUntilDate) {
      alert("Выберите дату актуальности предложения");
      return;
    }

    // Проверяем авторизацию и данные компании
    if (
      !user?.userId ||
      user?.userId === "00000000-0000-0000-0000-000000000000"
    ) {
      alert("Ошибка авторизации. Пожалуйста, войдите в систему заново.");
      return;
    }

    if (
      !user?.companyId ||
      user?.companyId === "0000" ||
      user?.companyId === 0
    ) {
      window.location.href = "/auth?from=company";
      return;
    }

    try {
      // Получаем заголовки авторизации
      const authHeaders = await authService.getAuthHeaders();

      // Находим соответствующий материал из tenderInfo для получения PurchReqLineId
      const material = tenderInfo.materials.find(
        (m) => m.MaterialId === proposal.materialId
      );
      if (!material) {
        alert("Ошибка: не найдена информация о материале");
        return;
      }

      const requestData = {
        PurchReqPriceId: 0,
        PurchReqId: tenderInfo.PurchReqId,
        PurchReqLineId: material.PurchReqLineId,
        MaterialId: proposal.materialId,
        ProviderId: user.companyId,
        UserId: user.userId,
        OfferQty: 1,
        OfferPrice: parseFloat(proposal.retailPrice),
        OfferSum: parseFloat(proposal.retailPrice),
        PurchReqPriceDate: new Date().toISOString(),
        ActualDate: validUntilDate.toISOString(),
        Description: proposal.comments || "",
        IsAccept: false,
        AcceptQty: 0,
        AcceptPrice: 0,
        IsOriginal: proposal.matchesRequest,
        PriceWithDelivery: proposal.priceType === "with-delivery",
      };

      console.log("📤 Отправка предложения:", requestData);
      console.log("🔑 Заголовки авторизации:", authHeaders);

      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqPrices`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            ...authHeaders,
          },
          body: JSON.stringify(requestData),
        }
      );

      if (response.ok) {
        const responseData = await response.json();
        const purchReqPriceId =
          responseData.PurchReqPriceId ||
          responseData.purchReqPriceId ||
          responseData.id;

        console.log(
          "✅ Предложение успешно отправлено, PurchReqPriceId:",
          purchReqPriceId
        );

        // Загружаем файлы, если они есть
        if (
          proposal.attachedFiles &&
          proposal.attachedFiles.length > 0 &&
          purchReqPriceId
        ) {
          console.log(
            `📎 Загружаем ${proposal.attachedFiles.length} файлов для предложения ${purchReqPriceId}...`
          );
          await uploadProposalFiles(purchReqPriceId, proposal.attachedFiles);
        }

        // Добавляем материал в список отправленных
        setSubmittedProposals(
          (prev) => new Set([...prev, proposal.materialId])
        );

        // Показываем уведомление об успехе
        setShowSuccessNotification(true);

        // Автоматически скрываем уведомление через 5 секунд
        setTimeout(() => {
          setShowSuccessNotification(false);
        }, 5000);
      } else {
        const errorData = await response.text();
        console.error(
          "❌ Ошибка при отправке предложения:",
          response.status,
          errorData
        );
        alert("Ошибка при отправке предложения. Попробуйте еще раз.");
      }
    } catch (error) {
      console.error("❌ Ошибка при отправке предложения:", error);
      alert("Произошла ошибка при отправке предложения. Попробуйте еще раз.");
    }
  };

  const handleBack = () => {
    router.push("/find-tender");
  };

  const handleOpenDateModal = () => {
    setIsDateModalOpen(true);
  };

  const handleCloseDateModal = () => {
    setIsDateModalOpen(false);
  };

  const handleDateSelect = (date) => {
    setValidUntilDate(date);
    setIsDateModalOpen(false);
  };

  // Функция для закрытия уведомления об успешной отправке
  const handleCloseSuccessNotification = () => {
    setShowSuccessNotification(false);
  };

  // Функции для управления уведомлениями об ошибках
  const showError = (message) => {
    setNotificationMessage(message);
    setShowErrorNotification(true);
    setTimeout(() => {
      setShowErrorNotification(false);
    }, 3000);
  };

  const handleCloseErrorNotification = () => {
    setShowErrorNotification(false);
  };

  // Функция для форматирования размера файла
  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Функция для обработки выбора файлов
  const handleFileSelect = (index, event) => {
    const files = Array.from(event.target.files);

    if (files.length === 0) return;

    // Проверяем размер файлов (максимум 10MB на файл)
    const maxSize = 10 * 1024 * 1024; // 10MB
    const oversizedFiles = files.filter((file) => file.size > maxSize);

    if (oversizedFiles.length > 0) {
      showError(
        `Файлы слишком большие. Максимальный размер: 10MB\n${oversizedFiles
          .map((f) => f.name)
          .join("\n")}`
      );
      return;
    }

    // Добавляем файлы к существующим
    setProposalData((prev) => {
      const newData = [...prev];
      const currentFiles = newData[index].attachedFiles || [];

      // Создаем объекты файлов с дополнительной информацией
      const newFiles = files.map((file) => ({
        file: file,
        name: file.name,
        size: file.size,
        type: file.type,
        id: Date.now() + Math.random(), // уникальный ID
      }));

      newData[index] = {
        ...newData[index],
        attachedFiles: [...currentFiles, ...newFiles],
      };

      return newData;
    });

    // Очищаем input для возможности повторного выбора того же файла
    event.target.value = "";
  };

  // Функция для удаления файла
  const handleRemoveFile = (proposalIndex, fileId) => {
    setProposalData((prev) => {
      const newData = [...prev];
      newData[proposalIndex] = {
        ...newData[proposalIndex],
        attachedFiles: newData[proposalIndex].attachedFiles.filter(
          (file) => file.id !== fileId
        ),
      };
      return newData;
    });
  };

  const formatDate = (dateString) => {
    if (!dateString) return "Не указано";
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const year = date.getFullYear().toString().slice(-2);
    return `${day}.${month}.${year}`;
  };

  if (isLoading) {
    return (
      <>
        <Header>
          <HeaderContent>
            <BackButton onClick={handleBack}>
              <img
                src="/icons/arrow_back_24px.svg"
                alt="Назад"
                style={{ width: "12px", height: "12px" }}
              />{" "}
              ВЕРНУТЬСЯ К СПИСКУ ТЕНДЕРОВ
            </BackButton>
          </HeaderContent>
        </Header>

        <TenderProposalContainer>
          <ContentContainer>
            <Title>Загрузка тендера...</Title>
            <NoTenderMessage>
              Пожалуйста, подождите. Загружаем информацию о тендере и
              материалах.
            </NoTenderMessage>
          </ContentContainer>
        </TenderProposalContainer>
      </>
    );
  }

  if (error) {
    return (
      <TenderProposalContainer>
        <ContentContainer>
          <Title>Ошибка загрузки</Title>
          <NoTenderMessage>
            Не удалось загрузить информацию о тендере: {error}
          </NoTenderMessage>
        </ContentContainer>
      </TenderProposalContainer>
    );
  }

  if (
    !tenderInfo ||
    !tenderInfo.materials ||
    tenderInfo.materials.length === 0
  ) {
    return (
      <>
        <Header>
          <HeaderContent>
            <BackButton onClick={handleBack}>
              <img
                src="/icons/arrow_back_24px.svg"
                alt="Назад"
                style={{ width: "12px", height: "12px" }}
              />{" "}
              ВЕРНУТЬСЯ К СПИСКУ ТЕНДЕРОВ
            </BackButton>
          </HeaderContent>
        </Header>

        <TenderProposalContainer>
          <ContentContainer>
            <Title>Тендер не найден</Title>
            <NoTenderMessage>
              Информация о тендере недоступна или в тендере нет материалов.
            </NoTenderMessage>
          </ContentContainer>
        </TenderProposalContainer>
      </>
    );
  }

  return (
    <>
      {/* Уведомление об успешной отправке предложения */}
      {showSuccessNotification && (
        <SuccessNotification>
          <SuccessNotificationText>
            Предложение успешно отправлено!
          </SuccessNotificationText>
          <SuccessNotificationClose onClick={handleCloseSuccessNotification}>
            ×
          </SuccessNotificationClose>
        </SuccessNotification>
      )}

      {/* Уведомление об ошибке */}
      {showErrorNotification && (
        <ErrorNotification>
          <SuccessNotificationText>
            {notificationMessage}
          </SuccessNotificationText>
          <SuccessNotificationClose onClick={handleCloseErrorNotification}>
            ×
          </SuccessNotificationClose>
        </ErrorNotification>
      )}

      <Header>
        <HeaderContent>
          <BackButton onClick={handleBack}>
            <img
              src="/icons/arrow_back_24px.svg"
              alt="Назад"
              style={{ width: "12px", height: "12px" }}
            />{" "}
            ВЕРНУТЬСЯ К СПИСКУ ТЕНДЕРОВ
          </BackButton>
        </HeaderContent>
      </Header>

      <TenderProposalContainer>
        <ContentContainer>
          <Title>Подача предложения на тендер</Title>

          {/* Информация о тендере */}
          {tenderDetails && (
            <ProductFormCard
              style={{
                marginBottom: "24px",
                backgroundColor: "#f0f8ff",
                padding: "16px",
              }}
            >
              <ProductInfo>
                <ProductTitle>{tenderDetails.PurchReqName}</ProductTitle>
                <Label>
                  Дата окончания:{" "}
                  {tenderDetails.PurchEndDate
                    ? new Date(tenderDetails.PurchEndDate).toLocaleDateString(
                        "ru-RU"
                      )
                    : "Не указано"}
                </Label>
                <Label>
                  Адрес доставки:{" "}
                  <em>{tenderDetails.DeliveryAddress || "Не указан"}</em>
                </Label>
                {tenderDetails.Description && (
                  <Label>Описание: {tenderDetails.Description}</Label>
                )}
              </ProductInfo>
            </ProductFormCard>
          )}

          <SectionTitle>Список закупок</SectionTitle>
          <Text>
            Укажите важные детали продукции, вашу цену, сроки и условия
            поставок.
          </Text>

          {proposalData.map((proposal, index) => (
            <ProductFormCard key={proposal.materialId}>
              {/* Секция с информацией о материале */}
              <MaterialInfoSection>
                <SectionHeader>Детали заказа</SectionHeader>

                {/* Фотографии материала и название */}
                <ProductHeader>
                  <MaterialImageThumbnails materialId={proposal.materialId} />
                  <ProductTitleContainer>
                    <ProductTitle style={{ marginBottom: "8px" }}>
                      {proposal.materialName}
                    </ProductTitle>
                    <ProductId style={{ fontSize: "16px", color: "#6c757d" }}>
                      ID: {proposal.materialId}
                    </ProductId>
                  </ProductTitleContainer>
                </ProductHeader>

                {/* Информация о материале в виде сетки */}
                <InfoGrid>
                  <InfoItem>
                    <InfoIcon>
                      <QuantityIcon />
                    </InfoIcon>
                    <InfoText>
                      <strong>{proposal.purchQty}</strong> {proposal.purchUnit}
                    </InfoText>
                  </InfoItem>

                  <InfoItem>
                    <InfoIcon>
                      <PriceIcon />
                    </InfoIcon>
                    <InfoText>
                      <strong>
                        {proposal.purchOpenPrice
                          ? `${proposal.purchOpenPrice} ₸ за ${proposal.purchUnit}`
                          : "Цена не указана"}
                      </strong>
                    </InfoText>
                  </InfoItem>

                  <InfoItem>
                    <InfoIcon>
                      <CheckIcon />
                    </InfoIcon>
                    <InfoText>
                      {proposal.originalOnly === true &&
                        "Соответствует запросу"}
                      {proposal.originalOnly === false && "Можно аналог"}
                    </InfoText>
                  </InfoItem>

                  <InfoItem>
                    <InfoIcon>
                      <DeliveryIcon />
                    </InfoIcon>
                    <InfoText>
                      {proposal.priceWithDelivery === true &&
                        "Цена с доставкой"}
                      {proposal.priceWithDelivery === false &&
                        "Цена без доставки"}
                    </InfoText>
                  </InfoItem>
                </InfoGrid>

                {/* Комментарии заказчика */}
                {proposal.Description && (
                  <div
                    style={{
                      marginTop: "16px",
                      padding: "12px",
                      backgroundColor: "#e3f2fd",
                      borderRadius: "8px",
                      borderLeft: "4px solid #2196f3",
                    }}
                  >
                    <InfoText>
                      <strong>Комментарии заказчика:</strong>
                      <br />
                      <em>{proposal.Description}</em>
                    </InfoText>
                  </div>
                )}
                {/* Прикрепленные файлы к материалу */}
                {materialPhotos[proposal.purchReqLineId] &&
                  materialPhotos[proposal.purchReqLineId].length > 0 && (
                    <div style={{ marginTop: "20px" }}>
                      <SectionHeader
                        style={{ fontSize: "18px", marginBottom: "12px" }}
                      >
                        Прикрепленные файлы к материалу
                      </SectionHeader>
                      <div
                        style={{
                          display: "flex",
                          flexWrap: "wrap",
                          gap: "12px",
                        }}
                      >
                        {materialPhotos[proposal.purchReqLineId].map(
                          (photo, photoIndex) => {
                            const fileType = getFileType(photo.FileName);
                            const isImage = fileType === "image";

                            return (
                              <div
                                key={photo.PurchReqTableFotoId}
                                style={{
                                  position: "relative",
                                  cursor: "pointer",
                                }}
                                onClick={() =>
                                  handleDownloadFile(
                                    photo.FileUrl,
                                    photo.FileName
                                  )
                                }
                                title={`Скачать ${photo.FileName}`}
                              >
                                {isImage ? (
                                  <img
                                    src={photo.FileUrl}
                                    alt={`Фото материала ${photoIndex + 1}`}
                                    style={{
                                      width: "120px",
                                      height: "120px",
                                      objectFit: "cover",
                                      borderRadius: "8px",
                                      border: "1px solid #ddd",
                                      transition: "all 0.2s ease",
                                      boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                                    }}
                                    onError={(e) => {
                                      e.target.style.display = "none";
                                    }}
                                    onMouseEnter={(e) => {
                                      e.target.style.transform = "scale(1.05)";
                                      e.target.style.boxShadow =
                                        "0 4px 8px rgba(0,0,0,0.15)";
                                    }}
                                    onMouseLeave={(e) => {
                                      e.target.style.transform = "scale(1)";
                                      e.target.style.boxShadow =
                                        "0 2px 4px rgba(0,0,0,0.1)";
                                    }}
                                  />
                                ) : (
                                  <div
                                    style={{
                                      width: "120px",
                                      height: "120px",
                                      borderRadius: "8px",
                                      border: "1px solid #ddd",
                                      display: "flex",
                                      flexDirection: "column",
                                      alignItems: "center",
                                      justifyContent: "center",
                                      backgroundColor: "#f8f9fa",
                                      transition: "all 0.2s ease",
                                      boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                                    }}
                                    onMouseEnter={(e) => {
                                      e.target.style.transform = "scale(1.05)";
                                      e.target.style.boxShadow =
                                        "0 4px 8px rgba(0,0,0,0.15)";
                                    }}
                                    onMouseLeave={(e) => {
                                      e.target.style.transform = "scale(1)";
                                      e.target.style.boxShadow =
                                        "0 2px 4px rgba(0,0,0,0.1)";
                                    }}
                                  >
                                    <div
                                      style={{
                                        fontSize: "36px",
                                        marginBottom: "8px",
                                      }}
                                    >
                                      {getFileIcon(photo.FileName)}
                                    </div>
                                    <div
                                      style={{
                                        fontSize: "10px",
                                        textAlign: "center",
                                        padding: "0 4px",
                                        color: "#666",
                                        wordBreak: "break-word",
                                      }}
                                    >
                                      {photo.FileName}
                                    </div>
                                  </div>
                                )}

                                {isImage && (
                                  <div
                                    style={{
                                      position: "absolute",
                                      bottom: "4px",
                                      left: "4px",
                                      right: "4px",
                                      background: "rgba(0,0,0,0.7)",
                                      color: "white",
                                      fontSize: "10px",
                                      padding: "2px 4px",
                                      borderRadius: "0 0 8px 8px",
                                      textAlign: "center",
                                      pointerEvents: "none",
                                    }}
                                  >
                                    {photo.FileName}
                                  </div>
                                )}

                                {/* Иконка скачивания */}
                                <div
                                  style={{
                                    position: "absolute",
                                    top: "4px",
                                    right: "4px",
                                    background: "rgba(0,0,0,0.7)",
                                    color: "white",
                                    borderRadius: "50%",
                                    width: "24px",
                                    height: "24px",
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    fontSize: "12px",
                                    pointerEvents: "none",
                                  }}
                                >
                                  ⬇
                                </div>
                              </div>
                            );
                          }
                        )}
                      </div>
                    </div>
                  )}
              </MaterialInfoSection>

              {/* Секция с формой предложения */}
              <ProposalFormSection>
                <SectionHeader>Ваше предложение</SectionHeader>

                <FormRow>
                  <SmallFormGroup>
                    <Input
                      type="number"
                      value={proposal.retailPrice}
                      onChange={(e) =>
                        handleProposalDataChange(
                          index,
                          "retailPrice",
                          e.target.value
                        )
                      }
                      placeholder="Ваша цена"
                      required
                    />
                    <span
                      style={{
                        position: "absolute",
                        right: "8px",
                        bottom: "8px",
                        color: "#656D78",
                        fontSize: "14px",
                      }}
                    >
                      ₸ за {proposal.purchUnit}
                    </span>
                  </SmallFormGroup>

                  <ActionButtonContainer>
                    <ActionButton
                      active={proposal.priceType === "with-delivery"}
                      onClick={() =>
                        handleProposalDataChange(
                          index,
                          "priceType",
                          "with-delivery"
                        )
                      }
                    >
                      Цена с доставкой
                    </ActionButton>
                    <ActionButton
                      active={proposal.priceType === "without-delivery"}
                      onClick={() =>
                        handleProposalDataChange(
                          index,
                          "priceType",
                          "without-delivery"
                        )
                      }
                    >
                      Без
                    </ActionButton>
                  </ActionButtonContainer>

                  <ActionButtonContainer>
                    <ActionButton
                      active={proposal.matchesRequest === true}
                      onClick={() =>
                        handleProposalDataChange(index, "matchesRequest", true)
                      }
                    >
                      Соответствует запросу
                    </ActionButton>
                    <ActionButton
                      active={proposal.matchesRequest === false}
                      onClick={() =>
                        handleProposalDataChange(index, "matchesRequest", false)
                      }
                    >
                      Аналог
                    </ActionButton>
                  </ActionButtonContainer>
                </FormRow>

                <Label
                  style={{
                    fontSize: "18px",
                    color: "#495057",
                    fontWeight: "600",
                  }}
                >
                  Комментарий к предложению
                </Label>

                <TextArea
                  value={proposal.comments}
                  onChange={(e) =>
                    handleProposalDataChange(index, "comments", e.target.value)
                  }
                  placeholder="Например: Предлагаемый нами продукт соответствует всем стандартам. "
                />

                <DateButton onClick={handleOpenDateModal}>
                  {validUntilDate
                    ? `Ценовое предложение актуально до: ${formatDate(
                        validUntilDate
                      )}`
                    : "Ценовое предложение актуально до:"}

                  <img src="/icons/Cell/Vector.svg" alt="Календарь" />
                </DateButton>
                <UploadButton
                  onClick={() =>
                    document.getElementById(`file-input-${index}`).click()
                  }
                >
                  <img src="/icons/Upload.svg" alt="Загрузить" />
                  <UploadText>Прикрепить файл</UploadText>
                </UploadButton>

                <HiddenFileInput
                  id={`file-input-${index}`}
                  type="file"
                  multiple
                  accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.gif,.zip,.rar"
                  onChange={(e) => handleFileSelect(index, e)}
                />

                {/* Список прикрепленных файлов */}
                {proposal.attachedFiles &&
                  proposal.attachedFiles.length > 0 && (
                    <AttachedFilesList>
                      {proposal.attachedFiles.map((fileObj) => (
                        <AttachedFileItem key={fileObj.id}>
                          <FileInfo>
                            <FileName>{fileObj.name}</FileName>
                            <FileSize>{formatFileSize(fileObj.size)}</FileSize>
                          </FileInfo>
                          <RemoveFileButton
                            onClick={() => handleRemoveFile(index, fileObj.id)}
                            title="Удалить файл"
                          >
                            ×
                          </RemoveFileButton>
                        </AttachedFileItem>
                      ))}
                    </AttachedFilesList>
                  )}
                <Label
                  style={{
                    fontSize: "16px",
                    color: "#6c757d",
                    fontStyle: "italic",
                  }}
                >
                  Фотографии, сертификаты качества, другая полезная информация
                </Label>

                {/* Кнопка отправки предложения для каждого материала */}
                <SubmitButton
                  onClick={() => handleSubmitSingleProposal(index)}
                  disabled={submittedProposals.has(proposal.materialId)}
                  style={{
                    margin: "16px 0 0 0",
                    width: "100%",
                    backgroundColor: submittedProposals.has(proposal.materialId)
                      ? "#6c757d"
                      : "#0066cc",
                    cursor: submittedProposals.has(proposal.materialId)
                      ? "not-allowed"
                      : "pointer",
                  }}
                >
                  {submittedProposals.has(proposal.materialId)
                    ? "Предложение отправлено"
                    : "Отправить предложение"}
                </SubmitButton>
              </ProposalFormSection>
            </ProductFormCard>
          ))}
        </ContentContainer>
      </TenderProposalContainer>

      {/* Модальное окно с календарем */}
      {isDateModalOpen && (
        <ModalOverlay onClick={handleCloseDateModal}>
          <ModalContent onClick={(e) => e.stopPropagation()}>
            <ModalHeader>
              <ModalTitle>Выберите дату актуальности предложения</ModalTitle>
              <CloseButton onClick={handleCloseDateModal}>×</CloseButton>
            </ModalHeader>

            <CalendarContainer>
              <DatePicker
                selected={validUntilDate}
                onChange={handleDateSelect}
                dateFormat="dd MMMM yyyy"
                locale="ru"
                calendarStartDay={1}
                inline
                minDate={new Date()} // Не позволяем выбирать прошедшие даты
              />
            </CalendarContainer>
          </ModalContent>
        </ModalOverlay>
      )}

      {/* Модальное окно галереи изображений */}
      {isGalleryOpen && galleryImages.length > 0 && (
        <GalleryModal onClick={closeGallery}>
          <GalleryContainer onClick={(e) => e.stopPropagation()}>
            <GalleryClose onClick={closeGallery}>×</GalleryClose>

            {galleryImages.length > 1 && (
              <>
                <GalleryNav
                  direction="prev"
                  onClick={prevImage}
                  disabled={galleryImages.length <= 1}
                >
                  ‹
                </GalleryNav>
                <GalleryNav
                  direction="next"
                  onClick={nextImage}
                  disabled={galleryImages.length <= 1}
                >
                  ›
                </GalleryNav>
              </>
            )}

            <GalleryImage
              src={galleryImages[currentImageIndex]?.src}
              alt={galleryImages[currentImageIndex]?.alt}
              title={galleryImages[currentImageIndex]?.title}
            />

            <GalleryImageName>
              {galleryImages[currentImageIndex]?.title}
            </GalleryImageName>

            {galleryImages.length > 1 && (
              <GalleryCounter>
                {currentImageIndex + 1} / {galleryImages.length}
              </GalleryCounter>
            )}
          </GalleryContainer>
        </GalleryModal>
      )}

      {/* Старое модальное окно для совместимости */}
      {selectedImage && (
        <GalleryModal onClick={() => setSelectedImage(null)}>
          <GalleryContainer onClick={(e) => e.stopPropagation()}>
            <GalleryClose onClick={() => setSelectedImage(null)}>
              ×
            </GalleryClose>
            <GalleryImage
              src={selectedImage.src}
              alt={selectedImage.alt}
              title={selectedImage.title}
            />
            <GalleryImageName>{selectedImage.title}</GalleryImageName>
          </GalleryContainer>
        </GalleryModal>
      )}
    </>
  );
};

export default TenderProposalClient;
