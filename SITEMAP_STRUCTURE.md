# Структура Sitemap для shop.sadi.kz

## Обзор

Проект использует многоуровневую структуру sitemap для оптимизации SEO и экономии Vercel invocations.

## Структура файлов

### 1. **sitemap-index.xml** (ГЛАВНЫЙ - указывать в Google Search Console)
- **URL**: `https://shop.sadi.kz/sitemap-index.xml`
- **Назначение**: Объединяет все остальные sitemap файлы
- **Файл**: `src/app/sitemap-index.xml/route.js`
- **Содержит ссылки на**:
  - sitemap.xml
  - sitemap-pages.xml
  - sitemap-categories.xml
  - sitemap-products.xml

### 2. **sitemap.xml** (Статические страницы)
- **URL**: `https://shop.sadi.kz/sitemap.xml`
- **Назначение**: Только уникальные статические страницы
- **Файл**: `src/app/sitemap.js`
- **Содержит** (~5 URL):
  - Главная страница (приоритет 1.0)
  - Создание тендера (приоритет 0.8)
  - Поиск тендеров (приоритет 0.8)
  - Форма тендера (приоритет 0.7)
  - О нас (приоритет 0.6)

### 3. **sitemap-pages.xml** (Страницы каталога)
- **URL**: `https://shop.sadi.kz/sitemap-pages.xml`
- **Назначение**: Страницы каталога с пагинацией
- **Файл**: `src/app/sitemap-pages.xml/route.js`
- **Содержит** (500 URL):
  - `/products/page/1` до `/products/page/500`
  - Приоритеты от 0.7 до 0.4 (убывают с номером страницы)

### 4. **sitemap-categories.xml** (Страницы категорий)
- **URL**: `https://shop.sadi.kz/sitemap-categories.xml`
- **Назначение**: Страницы категорий товаров
- **Файл**: `src/app/sitemap-categories.xml/route.js`
- **Содержит** (количество_категорий × 3 URL):
  - `/products/category/{Code}/page/1` (приоритет 0.8)
  - `/products/category/{Code}/page/2` (приоритет 0.7)
  - `/products/category/{Code}/page/3` (приоритет 0.6)

### 5. **sitemap-products.xml** (Отдельные товары)
- **URL**: `https://shop.sadi.kz/sitemap-products.xml`
- **Назначение**: Страницы отдельных товаров
- **Файл**: `src/app/sitemap-products.xml/route.js`
- **Содержит** (до 1000 URL):
  - `/product/{MaterialId}`
  - Приоритеты: 0.7 для популярных, 0.5 для обычных

## Настройка в Google Search Console

**Указывать только один URL**:
```
https://shop.sadi.kz/sitemap-index.xml
```

Google автоматически найдет и проиндексирует все дочерние sitemap.

## Robots.txt

Файл `src/app/robots.js` правильно настроен и указывает на главный sitemap-index.xml:

```
sitemap: ["https://shop.sadi.kz/sitemap-index.xml"]
```

## Оценка общего количества URL

- sitemap.xml: ~5 URL
- sitemap-pages.xml: 500 URL
- sitemap-categories.xml: ~количество_категорий × 3 URL
- sitemap-products.xml: до 1000 URL

**Общий лимит Google**: 50,000 URL на sitemap файл ✅

## Преимущества такой структуры

1. **Избегание дублирования**: Каждый URL только в одном sitemap
2. **Оптимизация для Vercel**: Ограниченное количество URL экономит invocations
3. **Легкое масштабирование**: Можно добавлять новые sitemap файлы
4. **SEO оптимизация**: Правильные приоритеты и частота обновлений
5. **Стандартный подход**: Соответствует рекомендациям Google

## Мониторинг

Отслеживать в Google Search Console:
- Количество отправленных URL
- Количество проиндексированных URL
- Ошибки индексации по каждому sitemap

## Обновления

Все sitemap обновляются автоматически при запросе с кэшированием на 1 час.
